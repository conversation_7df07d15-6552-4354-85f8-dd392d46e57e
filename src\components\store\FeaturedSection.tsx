'use client';

import React, { useState } from 'react';
import { Crown, ArrowRight, Star, Sparkles, Diamond, Check, Heart, Shield, RefreshCw } from 'lucide-react';
import { useTranslation } from '@/app/i18n/client';
import FeaturedCard, { FeaturedCardItem } from './FeaturedCard';
import { featuredCardsMockData } from '@/data/featuredCardsMockData';
import { EnhancedStoreCard, StoreCardHeader, StoreButton } from './EnhancedStoreCard';

interface FeaturedSectionProps {
  lang: string;
}

const FeaturedSection: React.FC<FeaturedSectionProps> = ({ lang }) => {
  const { t } = useTranslation(lang, 'translation');
  const [featuredItems, setFeaturedItems] = useState<FeaturedCardItem[]>(featuredCardsMockData);
  const [purchasingItemId, setPurchasingItemId] = useState<string | null>(null);

  const handlePurchase = async (item: FeaturedCardItem) => {
    try {
      setPurchasingItemId(item.id);

      // Simulate purchase process
      await new Promise(resolve => setTimeout(resolve, 1500));

      // Update the item as purchased
      setFeaturedItems(prev => prev.map(prevItem =>
        prevItem.id === item.id
          ? {
              ...prevItem,
              isPurchased: true,
              purchaseCount: (prevItem.purchaseCount || 0) + 1
            }
          : prevItem
      ));

      console.log('Purchase successful:', item.name);
    } catch (error) {
      console.error('Purchase failed:', error);
    } finally {
      setPurchasingItemId(null);
    }
  };





  return (
    <div className="space-y-6">

      {/* Featured Items Responsive Grid - Unified Layout */}
      <div className="grid grid-cols-1 xs:grid-cols-2 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 md:gap-6">
        {featuredItems.map((item) => (
          <FeaturedCard
            key={item.id}
            item={item}
            onPurchase={handlePurchase}
            isLoading={purchasingItemId === item.id}
            variant="responsive"
          />
        ))}
      </div>


      {/* Enhanced Premium Subscription Promotion with ISFJ warmth */}
      <EnhancedStoreCard
        gradient="from-indigo-500/15 via-purple-500/15 to-pink-500/15"
        glowEffect={true}
        vipGlow={true}
        neonReflection={true}
        className="mt-6"
      >
        <div className="relative">
          {/* Background decoration */}
          <div className="absolute inset-0 bg-gradient-to-br from-yellow-400/10 via-purple-500/10 to-pink-500/10 rounded-xl blur-xl" />
          
          {/* Content */}
          <div className="relative">
            {/* Header with premium badge */}
            <div className="text-center mb-8">
              <div className="inline-flex items-center gap-2 bg-gradient-to-r from-yellow-400 to-orange-500 text-white px-4 py-2 rounded-full text-sm font-bold mb-4 shadow-lg">
                <Star className="w-4 h-4" fill="currentColor" />
                {t('store.subscriptions.mostPopular')}
              </div>
              
              <h2 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-2">
                {t('store.subscriptions.diamond.name')}
              </h2>
              <p className="text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
                {t('store.subscriptions.diamond.description')}
              </p>
            </div>

            {/* Price and CTA section */}
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
              {/* Price card */}
              <div className="lg:col-span-1">
                <div className="bg-gradient-to-br from-yellow-500/20 to-orange-500/20 border border-yellow-500/30 rounded-xl p-6 text-center h-full flex flex-col justify-center">
                  <div className="text-4xl font-bold text-yellow-400 mb-2">{t('store.featured.currentPrice')}</div>
                  <div className="text-sm text-foreground/60 mb-3">{t('store.featured.perMonth')}</div>
                  <div className="space-y-2">
                    <div className="text-sm text-green-400 font-medium">
                      <span className="line-through text-gray-500 mr-2">{t('store.featured.originalPrice')}</span>
                      {t('store.featured.discount')}
                    </div>
                    <div className="text-xs text-foreground/60">
                      {t('store.subscriptions.limitedTimeOffer')}
                    </div>
                  </div>
                </div>
              </div>

              {/* Key benefits grid */}
              <div className="lg:col-span-2">
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  {/* Benefit 1: Unlimited */}
                  <div className="bg-gradient-to-br from-purple-500/20 to-pink-500/20 border border-purple-500/30 rounded-xl p-4 flex items-start gap-3">
                    <div className="w-10 h-10 bg-gradient-to-br from-purple-400 to-pink-500 rounded-lg flex items-center justify-center flex-shrink-0">
                      <Sparkles className="w-5 h-5 text-white" />
                    </div>
                    <div>
                      <div className="font-semibold text-purple-400 mb-1">{t('store.featured.unlimited')}</div>
                      <div className="text-xs text-foreground/60">{t('store.featured.unlimitedDescription')}</div>
                    </div>
                  </div>

                  {/* Benefit 2: Premium Features */}
                  <div className="bg-gradient-to-br from-pink-500/20 to-rose-500/20 border border-pink-500/30 rounded-xl p-4 flex items-start gap-3">
                    <div className="w-10 h-10 bg-gradient-to-br from-pink-400 to-rose-500 rounded-lg flex items-center justify-center flex-shrink-0">
                      <Diamond className="w-5 h-5 text-white" fill="currentColor" />
                    </div>
                    <div>
                      <div className="font-semibold text-pink-400 mb-1">{t('store.featured.premium')}</div>
                      <div className="text-xs text-foreground/60">{t('store.featured.premiumDescription')}</div>
                    </div>
                  </div>

                  {/* Benefit 3: Exclusive Access */}
                  <div className="bg-gradient-to-br from-indigo-500/20 to-purple-500/20 border border-indigo-500/30 rounded-xl p-4 flex items-start gap-3">
                    <div className="w-10 h-10 bg-gradient-to-br from-indigo-400 to-purple-500 rounded-lg flex items-center justify-center flex-shrink-0">
                      <Crown className="w-5 h-5 text-white" />
                    </div>
                    <div>
                      <div className="font-semibold text-indigo-400 mb-1">{t('store.featured.exclusive')}</div>
                      <div className="text-xs text-foreground/60">{t('store.featured.exclusiveDescription')}</div>
                    </div>
                  </div>

                  {/* Benefit 4: Creator Rewards */}
                  <div className="bg-gradient-to-br from-green-500/20 to-emerald-500/20 border border-green-500/30 rounded-xl p-4 flex items-start gap-3">
                    <div className="w-10 h-10 bg-gradient-to-br from-green-400 to-emerald-500 rounded-lg flex items-center justify-center flex-shrink-0">
                      <Heart className="w-5 h-5 text-white" />
                    </div>
                    <div>
                      <div className="font-semibold text-green-400 mb-1">{t('store.featured.creatorBenefits')}</div>
                      <div className="text-xs text-foreground/60">{t('store.featured.creatorBenefitsDescription')}</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Full benefits list */}
            <div className="bg-gradient-to-r from-gray-50/50 to-gray-100/50 dark:from-gray-800/50 dark:to-gray-700/50 rounded-xl p-6 mb-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4 flex items-center gap-2">
                <Check className="w-5 h-5 text-green-500" />
                {t('store.subscriptions.allFeatures')}
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-3">
                {[
                  t('store.subscriptions.diamond.features.0'),
                  t('store.subscriptions.diamond.features.3'),
                  t('store.subscriptions.diamond.features.4'),
                  t('store.subscriptions.diamond.features.6'),
                  t('store.subscriptions.diamond.features.7'),
                  t('store.subscriptions.diamond.features.8'),
                  t('store.subscriptions.diamond.features.9'),
                  t('store.subscriptions.diamond.features.11'),
                ].map((feature, index) => (
                  <div key={index} className="flex items-start gap-2">
                    <Check className="w-4 h-4 text-green-500 mt-0.5 flex-shrink-0" />
                    <span className="text-sm text-gray-700 dark:text-gray-300">{feature}</span>
                  </div>
                ))}
              </div>
            </div>

            {/* CTA Button */}
            <div className="flex justify-center">
              <StoreButton
                variant="primary"
                size="lg"
                neonGlow={true}
                className="px-8 py-3 bg-gradient-to-r from-yellow-400 to-orange-500 hover:from-yellow-500 hover:to-orange-600 text-white font-bold shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200"
              >
                <div className="flex items-center justify-center gap-2 whitespace-nowrap">
                  <Crown className="w-5 h-5 flex-shrink-0" />
                  <span className="flex-shrink-0">{t('store.subscriptions.upgradeToDiamond')}</span>
                  <ArrowRight className="w-5 h-5 flex-shrink-0" />
                </div>
              </StoreButton>
            </div>

            {/* Trust badges */}
            <div className="mt-6 flex flex-wrap items-center justify-center gap-4 text-xs text-gray-500 dark:text-gray-400">
              <div className="flex items-center gap-1">
                <Check className="w-3 h-3 text-green-500" />
                {t('store.subscriptions.instantAccess')}
              </div>
              <div className="flex items-center gap-1">
                <Shield className="w-3 h-3 text-blue-500" />
                {t('store.subscriptions.secureBilling')}
              </div>
              <div className="flex items-center gap-1">
                <RefreshCw className="w-3 h-3 text-purple-500" />
                {t('store.subscriptions.cancelAnytime')}
              </div>
            </div>
          </div>
        </div>
      </EnhancedStoreCard>
    </div>
  );
};

export default FeaturedSection;

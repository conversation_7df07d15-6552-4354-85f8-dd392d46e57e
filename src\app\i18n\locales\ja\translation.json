{"nav": {"createCharacter": "Create Character", "dailyTasks": "Daily Tasks", "battlePass": "Battle Pass", "home": "Home", "chats": "Chats", "contacts": "Contacts", "discover": "Discover", "profile": "Profile"}, "common": {"tagline": "温かいAIコンパニオン", "streak": "インタラクション連続記録", "days": "日", "recentChats": "最近のチャット", "viewAllChats": "すべてのチャットを見る", "promoTitle": "専用ささやき空間をアンロック", "promoSubtitle": "ダイアモンドパスにアップグレードして無制限のAIインタラクションを", "upgradeNow": "今すぐアップグレード", "member": "メンバー", "searchPlaceholder": "キャラクター、デザイナーを検索...", "cancel": "キャンセル", "back": "戻る", "continue": "続行", "loading": "読み込み中"}, "sidebar": {"interactionStreak": "インタラクション連続記録", "todaysJourney": "今日のジャーニー", "viewJourney": "ジャーニーを表示", "recentChats": "最近のチャット", "discoverMore": "もっと発見", "shiningMoments": "輝く瞬間", "discoverCharacters": "キャラクターを発見", "memoriesSquare": "思い出広場", "community": "コミュニティ", "usefulLinks": "便利なリンク", "termsOfUse": "利用規約", "privacyPolicy": "プライバシーポリシー", "sitemap": "サイトマップ", "contactUs": "お問い合わせ", "freeUser": "フリーユーザー", "login": "ログイン", "register": "登録", "logout": "ログアウト"}, "streak": {"start": "連続記録の旅を始めよう", "daysLeft": "{{target}}日のマイルストーンまであと{{days}}日", "milestone": "{{target}}日のマイルストーン達成！", "currentDays": "{{days}}日", "milestonemultiplier": "マイルストーンギフト倍率：{{multiplier}}"}, "tokens": {"alphane": "きらめく塵", "endora": "喜びの結晶", "serotile": "記憶のパズル", "oxytol": "絆の露", "currency": "通貨"}, "characterEdit": {"title": "キャラクター編集", "subtitle": "キャラクターの詳細と設定を変更する", "saveChanges": "変更を保存", "savingChanges": "変更を保存中...", "characterUpdated": "キャラクターが正常に更新されました", "navigation": {"previous": "前へ", "next": "次へ", "finish": "変更を保存", "stepOf": "ステップ {{current}} / {{total}}", "stepComplete": "ステップ完了", "stepIncomplete": "必須フィールドを完成してください"}}, "characterCreation": {"title": "キャラクター作成", "subtitle": "AIを活用したキャラクター作成で想像力を現実にしましょう", "functions": {"flash": {"title": "フラッシュ", "description": "60秒でクイックキャラクター生成", "subtitle": "60秒でキャラクター作成"}, "customize": {"title": "カスタマイズ", "description": "完全コントロールの詳細キャラクターカスタマイゼーション"}, "import": {"title": "インポート", "description": "SillyTavernカードや外部ファイルからインポート"}}, "steps": {"basics": {"title": "基本情報", "description": "名前、性別、画像、外見、性格、タグ"}, "personality": {"title": "行動と知識", "description": "行動、知識、背景、可視性"}, "advanced": {"title": "高度な設定", "description": "クイックスタートまたは高度なストーリーチャプター"}}, "flash": {"characterName": "キャラクター名", "characterConcept": "キャラクターコンセプト", "namePlaceholder": "キャラクター名を入力（オプション - AIが自動生成します）", "conceptPlaceholder": "キャラクターの説明：外見、性格、行動、知識、背景ストーリー。残りの詳細はAIが生成します...", "generateButton": "🚀 キャラクター生成", "generatingText": "魔法を生成中...", "aiFeatures": "✨ AIが自動的にこれらの側面を生成します：", "previewTitle": "✨ 生成されたキャラクタープレビュー", "regenerationsLeft": "残り再生成回数：", "nameTag": "✨ 名前", "appearanceTag": "👤 外見", "personalityTag": "🎭 性格", "behaviorTag": "🎪 行動", "knowledgeTag": "🧠 知識", "storyTag": "📖 ストーリー"}, "filters": {"gender": "性別", "pointOfView": "視点", "any": "任意", "female": "女性", "male": "男性", "other": "その他", "femalePOV": "女性視点", "malePOV": "男性視点", "otherPOV": "その他視点"}, "import": {"title": "キャラクターインポート", "subtitle": "SillyTavernカードやスクリプトファイルをアップロードしてキャラクターを作成", "sillyTavern": "📄 SillyTavernキャラクターカード", "uploadArea": {"title": "クリックまたはドラッグしてアップロード", "subtitle": "SillyTavern .jsonファイルをサポート", "button": "📄 JSONファイルを選択", "fileSelected": "ファイルが選択されました。クリックして別のファイルを選択", "readyToImport": "✅ インポート準備完了"}, "features": {"title": "📋 インポート機能", "characterCards": "📄 キャラクターカード", "autoExtract": "キャラクターデータの自動抽出", "fillDescription": "説明と背景の自動入力"}, "selectCharacter": {"title": "🎯 キャラクター選択", "subtitle": "🎭 インポートするキャラクターを選択（{{count}}個見つかりました）"}, "scriptFiles": {"title": "📝 スクリプトファイル", "uploaded": "スクリプトのアップロードが完了しました", "uploadTitle": "スクリプトファイルをアップロード", "uploadSubtitle": "ドラッグ&ドロップまたはクリックして.txtファイルをアップロード（最大16KB）"}, "importFeatures": {"title": "📋 インポート機能", "characterCards": "📄 キャラクターカード", "multipleFiles": "複数の.jsonファイルを一度に", "singleFileMultiple": "複数キャラクターを含む単一.jsonファイル", "sillyTavernSupport": "SillyTavern形式サポート", "txtSupport": ".txtファイルサポート（最大16KB）", "autoExtract": "キャラクターデータの自動抽出", "fillBackground": "説明と背景の自動入力"}, "filters": {"gender": "性別", "pointOfView": "視点", "any": "任意", "female": "女性", "male": "男性", "other": "その他", "femalePOV": "女性視点", "malePOV": "男性視点", "otherPOV": "その他視点"}}, "basics": {"title": "キャラクター基本情報", "subtitle": "キャラクターの基本情報を設定", "characterName": "キャラクター名", "namePlaceholder": "キャラクター名を入力...", "nameOriginPlaceholder": "名前の由来や意味を入力（オプション）", "genderPov": "性別と視点", "appearance": "外見", "appearancePlaceholder": "体格や顔の特徴、服装や装身具...", "personality": "性格", "traits": "特性", "traitsPlaceholder": "キャラクターの特性を記述：勇敢、親切、頑固、好奇心旺盛、忠実、いたずら好き、完璧主義、冒険的、内向的、楽観的...", "mind": "思考", "mindPlaceholder": "思考パターンを記述：分析的、創造的、論理的、直感的、戦略的、衝動的、系統的、抽象的、実用的、哲学的...", "emotion": "感情", "emotionPlaceholder": "感情パターンを記述：共感的、情熱的、冷静、繊細、表現力豊か、控えめ、温かい、激しい、優しい、劇的、安定...", "characterSettings": "キャラクター設定", "characterSettingsPlaceholder": "キャラクターの背景設定、身分、職業、特殊な状況を記述...", "additionalTags": "追加タグ", "tagPlaceholder": "タグを入力（例：友好的、神秘的、戦士）", "addTag": "タグを追加"}, "personality": {"title": "行動と知識", "subtitle": "キャラクターの行動パターンと知識ベースを定義", "behaviour": "行動", "defaultGreeting": "デフォルト挨拶メッセージ", "greetingPlaceholder": "ユーザーがチャットを始めたときにキャラクターが最初に言うメッセージを入力...", "speechStyle": "話し方スタイル", "speechStylePlaceholder": "キャラクターの話し方のスタイルとトーンを記述...", "facialExpressions": "表情", "facialExpressionsPlaceholder": "キャラクターの表情や癖を記述...", "bodyLanguage": "ボディランゲージ", "bodyLanguagePlaceholder": "キャラクターのボディランゲージやジェスチャーを記述...", "knowledge": "知識", "knowledgeBase": "知識ベース", "knowledgePlaceholder": "キャラクターが知っているべき一般知識、事実、背景情報を入力...", "uploadKnowledgeFiles": "知識ファイルアップロード", "uploadKnowledgeDesc": ".json、.md、.txt形式のファイルをサポート", "uploadKnowledgeButton": "知識ファイルをアップロードするにはクリック", "uploadedFiles": "アップロード済みファイル：", "deleteFile": "ファイルを削除", "goodAt": "得意なこと", "goodAtPlaceholder": "例：料理、数学、剣術、魔法の呪文、リーダーシップ...", "badAt": "苦手なこと", "badAtPlaceholder": "例：嘘をつくこと、技術、水泳、人前でのスピーチ、名前を覚えること...", "add": "追加", "backgroundStory": "背景ストーリー", "relationshipWithPlayer": "プレイヤーとの関係", "relationshipPlaceholder": "キャラクターとプレイヤーの関係の背景を記述...", "importantExperiences": "重要な過去の経験", "experiencesPlaceholder": "キャラクターの重要な経験と背景ストーリーを記述...", "visibility": "公開設定", "public": "公開", "publicDesc": "誰でも見てチャットできます", "unlisted": "非表示", "unlistedDesc": "直接リンクでのみアクセス可能", "private": "プライベート", "privateDesc": "あなたのみアクセス可能"}, "advanced": {"title": "高度な設定", "subtitle": "クイックスタートまたは高度なストーリーチャプター", "readyToChat": "チャットを始める準備はできましたか？", "readyToChatDesc": "あなたのキャラクターの準備ができました！下のボタンをクリックして会話を始めましょう。", "readyToChatButton": "今すぐチャットを開始", "advancedStoryBuilding": "高度なストーリー構築", "advancedStoryDesc": "詳細なストーリーチャプターを作成し、高度な設定をカスタマイズします。", "checkPassButton": "パスと高度な設定を確認", "passVerified": "パス認証済み", "passVerifiedDesc": "アクティブパスまたはダイヤモンドパスが検出されました。詳細なストーリーチャプターを構築し、高度な機能を使用できます。", "noActivePass": "アクティブなパスがありません", "noActivePassDesc": "高度な機能にはアクティブなパスが必要です。ストーリー構築ツールにアクセスするためにアップグレードしてください。", "uploadScript": "プロット分析用スクリプトアップロード", "uploadScriptButton": "スクリプトファイルをアップロード", "uploadScriptDesc": ".txt、.md、.json形式のスクリプトファイルをサポート", "storyChapters": "ストーリーチャプターとキャラクター挨拶", "updatedGreeting": "更新された挨拶", "greetingAfterChapter": "チャプター完了後の挨拶", "greetingAfterPlaceholder": "特定のチャプター完了後の新しい挨拶を入力...", "triggerAfterChapter": "チャプター後にトリガー", "chapter": "チャプター", "addChapter": "チャプターを追加", "addGreeting": "挨拶を追加"}, "imageUpload": {"characterImages": "キャラクター画像", "characterPortrait": "キャラクターポートレート", "avatarAutoCropped": "アバター（自動クロップ）", "uploadCharacterImage": "キャラクター画像をアップロード", "clickOrDragToUpload": "クリックまたはドラッグしてアップロード", "characterImageUploaded": "キャラクター画像がアップロードされました", "clickToChangeImage": "クリックして画像を変更", "avatarReady": "アバターの準備完了", "croppedFromCharacterImage": "キャラクター画像からクロップ", "uploadCharacterImageFirst": "まずキャラクター画像をアップロードしてください", "avatarWillBeAutoCropped": "アバターは自動でクロップされます", "avatarPreview": "アバタープレビュー：", "avatarSetupComplete": "✅ アバター設定完了", "sourceCroppedFromCharacterImage": "ソース：キャラクター画像からクロップ"}, "cropper": {"avatar": {"title": "アバターをクロップ", "character": "キャラクター画像をクロップ", "description": "選択ボックスをドラッグして{{type}}エリアを調整、比率：{{ratio}}", "selectAspectRatio": "アスペクト比を選択", "avatarPreview": "アバタープレビュー", "characterPreview": "キャラクタープレビュー", "avatarInfo": "アバター情報", "characterRatio": "キャラクター比率", "dragToMove": "ドラッグしてクロップエリアを移動", "dragCorners": "角をドラッグしてサイズ調整", "mouseWheel": "マウスホイールでクロップエリアをズーム", "resetArea": "エリアをリセット", "resizing": "サイズ調整中", "dragToMoveText": "ドラッグして移動", "cancel": "キャンセル", "confirmCrop": "クロップを確認", "ratios": {"5:6": "クラシックポートレート", "5:8": "全身ポートレート"}}}, "personalityTags": {"addCustomTag": "カスタムタグを追加", "customTagPlaceholder": "性格特性を入力...", "addButton": "追加", "addTagHint": "Enterキーを押すか追加ボタンをクリックしてカスタムタグを追加", "presetTags": {"gentle": "優しい", "lively": "活発", "mysterious": "神秘的", "calm": "冷静", "humorous": "ユーモラス", "serious": "真面目", "innocent": "純真", "mature": "大人", "cheerful": "明るい", "introverted": "内向的", "curious": "好奇心旺盛", "kind": "親切", "brave": "勇敢", "wise": "賢い", "strong": "強い", "elegant": "上品", "cute": "可愛い", "stern": "厳格", "optimistic": "楽観的", "pessimistic": "悲観的", "romantic": "ロマンチック", "realistic": "現実的", "rational": "理性的", "emotional": "感情的"}}, "navigation": {"previous": "前へ", "next": "次へ", "stepOf": "ステップ{{current}} / {{total}}", "finish": "キャラクター作成", "stepComplete": "このステップは完了しています", "stepIncomplete": "続行するには、すべての必須フィールドを完了してください"}}, "storyCreation": {"pageTitle": "ストーリー作成 - Alphane", "pageDescription": "キャラクターのための詳細なストーリープロットとチャプターを作成し、インタラクティブ体験を向上させます", "title": "ストーリー作成", "subtitle": "キャラクターのために魅力的なストーリー体験を作成", "selectCharacter": {"title": "ストーリーを作成", "subtitle": "冒険を始めるキャラクターを選択してください", "searchPlaceholder": "名前や説明でキャラクターを検索...", "selectYourCharacter": "キャラクターを選択", "viewMode": "表示：", "filters": "フィルター", "storyStatus": "ストーリーステータス", "allCharacters": "すべてのキャラクター", "withStories": "ストーリーあり", "withoutStories": "ストーリーなし", "gender": "性別", "allGenders": "すべての性別", "male": "男性", "female": "女性", "other": "その他", "pointOfView": "視点", "allPOV": "すべての視点", "firstPerson": "一人称", "secondPerson": "二人称", "thirdPerson": "三人称", "filterByTags": "タグでフィルター", "addTagPlaceholder": "フィルター用タグを追加...", "add": "追加", "charactersFound": "{{count}}個のキャラクターが見つかりました", "characterFound": "{{count}}個のキャラクターが見つかりました", "sortBy": "並び替え：", "name": "名前", "fans": "ファン", "heatScore": "人気度", "trend": "トレンド", "lastUpdated": "最終更新", "totalStories": "総ストーリー数", "totalLikes": "総いいね数", "totalPlays": "総再生数", "noMatchingCharacters": "一致するキャラクターがありません", "noMatchingCharactersDesc": "検索やフィルター条件を調整して、ストーリーに最適なキャラクターを見つけてください。", "clearAllFilters": "すべてのフィルターをクリア", "noCharactersYet": "まだキャラクターがありません", "noCharactersYetDesc": "最初のAIキャラクターを作成して、素晴らしいストーリーと体験の構築を始めましょう。", "createFirstCharacter": "最初のキャラクターを作成", "noDescriptionAvailable": "説明がありません", "createStory": "ストーリーを作成"}, "success": {"storyCreated": "ストーリーが正常に作成されました！"}, "error": {"failedToGenerate": "ストーリーの生成に失敗しました。もう一度お試しください", "failedToCreate": "ストーリーの作成に失敗しました。もう一度お試しください", "characterNotFound": "キャラクターが見つかりません", "characterNotFoundDescription": "お探しのキャラクターは存在しないか、削除されています。", "backToCharacterSelection": "キャラクター選択に戻る"}, "functions": {"flash": {"title": "フラッシュ", "description": "AI高速ストーリー生成", "placeholder": "ストーリーのコンセプトを一文で説明してください...", "generate": "ストーリーを生成", "generating": "ストーリーを生成中...", "example": "例：古代ローマでのロマンチックな冒険", "needInspiration": "インスピレーションが必要ですか？これらの例を試してみてください：", "helpText": "💡 AIがシーン設定、キャラクター心理、インタラクション動態を含む完全なストーリーを生成します"}, "customize": {"title": "カスタマイズ", "description": "詳細ストーリーカスタマイズ", "steps": {"basics": "ストーリーカバー", "details": "設定テーマ", "worldSetting": "世界設定", "storyFlow": "ストーリーフロー", "objectivesSubjectives": "目的と主観設定"}}}, "steps": {"chapters": {"title": "チャプターとフロー", "description": "ストーリーチャプターを作成してフローを設定", "storyFlow": "ストーリーフロー", "chapterTitle": "チャプタータイトル", "chapterDescription": "チャプター説明", "chapterContent": "チャプター内容", "backgroundSetting": "背景設定", "backgroundSettingPlaceholder": "このチャプターの設定と雰囲気を説明...", "backgroundImage": "背景画像", "uploadBackgroundImage": "背景画像をアップロード", "backgroundImageUploaded": "背景画像がアップロードされました", "clickToChangeImage": "クリックして画像を変更", "clickOrDragToUpload": "クリックまたはドラッグしてアップロード", "useWorldSettingImage": "世界設定画像を使用", "usingWorldSettingImage": "世界設定画像を使用中", "inheritedFromWorldSetting": "世界設定から継承", "completionEffects": "完了効果", "bondPointsChange": "絆ポイント変化", "bondPointsPlaceholder": "例：+10, -5, 0", "greetingChange": "挨拶変化", "greetingChangePlaceholder": "このチャプター完了後の新しい挨拶...", "characterMoodChange": "キャラクター気分変化", "characterMoodChangePlaceholder": "キャラクターの気分変化を説明...", "customEffects": "カスタム効果", "customEffectsPlaceholder": "その他の効果や変化...", "choices": "チャプター選択肢", "addChoice": "選択肢を追加", "choiceText": "選択肢テキスト", "choiceDescription": "選択肢説明", "choiceTriggerCondition": "トリガー条件", "choiceTriggerConditionPlaceholder": "例：絆レベル ≥ 3、前のタスク完了済み...", "nextChapter": "次のチャプター", "selectNextChapter": "次のチャプターを選択...", "addMainChapter": "メインチャプターを追加", "addBranchChapter": "ブランチチャプターを追加", "removeChapter": "チャプターを削除", "noChapters": "まだチャプターがありません", "addFirstChapter": "最初のチャプターを追加"}, "objectives": {"title": "客観的要素", "description": "シーンの客観的環境と背景要素を定義", "selectChapter": "チャプターを選択", "selectChapterDescription": "左からチャプターを選択して客観的要素を編集", "scene": {"title": "シーン層", "timeElements": "時間要素", "season": "季節", "selectSeason": "季節を選択...", "seasons": {"spring": "春", "summer": "夏", "autumn": "秋", "winter": "冬"}, "timeOfDay": "時間帯", "selectTimeOfDay": "時間帯を選択...", "timesOfDay": {"dawn": "夜明け", "morning": "朝", "noon": "正午", "afternoon": "午後", "evening": "夕方", "night": "夜", "midnight": "真夜中"}, "duration": "持続時間", "durationPlaceholder": "例：2時間、一日中、一瞬...", "specialDate": "特別な日", "specialDatePlaceholder": "例：クリスマス、誕生日、祭り...", "spatialElements": "空間要素", "location": "場所", "locationPlaceholder": "例：学校の屋上、居心地の良いカフェ、魔法の森...", "atmosphere": "雰囲気", "atmospherePlaceholder": "空間の気分と感覚を説明...", "environmentalElements": "環境要素", "weather": {"sunny": "晴れ", "cloudy": "曇り", "rainy": "雨", "stormy": "嵐", "snowy": "雪", "foggy": "霧"}, "selectWeather": "天気を選択...", "lighting": {"bright": "明るい", "dim": "薄暗い", "dark": "暗い", "candlelit": "ろうそくの明かり", "neon": "ネオン"}, "selectLighting": "照明を選択..."}, "antecedent": {"title": "前情層", "macroHistory": "マクロ歴史", "macroHistoryPlaceholder": "より広い歴史的文脈と世界の出来事を説明...", "characterPast": "キャラクター過去", "characterPastPlaceholder": "関連するキャラクターの背景と過去の経験...", "immediateTrigger": "即座のトリガー", "immediateTriggerPlaceholder": "このチャプターのイベントを即座に引き起こしたものは？"}}, "subjectives": {"title": "主観的要素", "description": "キャラクター心理とインタラクション動態を構成", "selectChapter": "チャプターを選択", "selectChapterDescription": "左からチャプターを選択して主観的要素を編集", "character": {"title": "キャラクター層", "mentalModel": "精神モデル", "coreValues": "核心価値観", "coreValuesPlaceholder": "キャラクターが最も重視するものは？", "thinkingMode": "思考モード", "thinkingModePlaceholder": "キャラクターはどのように情報を処理しますか？", "decisionLogic": "決定論理", "decisionLogicPlaceholder": "キャラクターはどのように決定を下しますか？", "emotionalBaseline": "感情ベースライン", "displayedEmotion": "表示される感情", "displayedEmotionPlaceholder": "キャラクターはどんな感情を示しますか？", "hiddenEmotion": "隠された感情", "hiddenEmotionPlaceholder": "キャラクターはどんな感情を隠していますか？", "emotionalIntensity": "感情強度", "emotionalStability": "感情安定性"}, "interaction": {"title": "インタラクション層", "dialogueStrategy": "対話戦略", "communicationStyle": "コミュニケーションスタイル", "communicationStylePlaceholder": "キャラクターはどのようにコミュニケーションを取りますか？", "responsePattern": "反応パターン", "responsePatternPlaceholder": "キャラクターは通常どのように反応しますか？", "goalOrientation": "目標指向", "primaryGoal": "主要目標", "primaryGoalPlaceholder": "キャラクターは何を達成しようとしていますか？", "conflictResolution": "対立解決", "conflictResolutionPlaceholder": "キャラクターは対立をどのように処理しますか？", "initialGoodwill": "初期好感度", "trustLevel": "信頼レベル", "intimacyLevel": "親密レベル", "intimacyLevelPlaceholder": "例：見知らぬ人、友達、親密、恋人...", "displayedIntent": "表示される意図", "displayedIntentPlaceholder": "彼らが求めているように見えるもの...", "hiddenIntent": "隠された意図", "hiddenIntentPlaceholder": "彼らが本当に求めているもの..."}}, "objectivesSubjectives": {"title": "目的と主観設定", "description": "チャプターの目的とキャラクター心理を構成", "selectChapter": "チャプターを選択", "selectChapterDescription": "左からチャプターを選択して目的と主観要素を編集"}}, "worldSetting": {"title": "世界設定", "description": "ストーリーの世界と環境設定を構成", "basicInfo": {"title": "基本情報", "storyName": "ストーリー名", "storyNamePlaceholder": "ストーリーの名前を入力...", "storyDescription": "ストーリー説明", "storyDescriptionPlaceholder": "外部表示用のストーリーの簡潔な説明を入力してください...", "openingMessage": "オープニングメッセージ", "openingMessagePlaceholder": "ユーザーがこのストーリーを開始するときに表示されるオープニングメッセージを入力...", "storyTags": "ストーリータグ", "tagPlaceholder": "タグを入力（例：ロマンス、アドベンチャー、ミステリー）", "addTag": "タグを追加", "removeTag": "削除", "coverImage": "カバー画像", "uploadCoverImage": "カバー画像をアップロード", "coverImageUploaded": "カバー画像がアップロードされました", "clickToChangeImage": "クリックして画像を変更", "clickOrDragToUpload": "クリックまたはドラッグしてアップロード"}, "quickSetup": {"title": "クイック設定", "worldOverview": "世界観概要", "worldOverviewPlaceholder": "ストーリー世界の全体的な設定を説明...", "storyBackground": "ストーリー背景", "storyBackgroundPlaceholder": "ストーリーの背景状況を説明..."}, "basicSettings": {"title": "基本設定", "historicalEra": "歴史時代", "historicalEraPlaceholder": "例：ビクトリア朝時代、黄金の20年代、2077年、星間探査時代...", "geographicEnvironment": "地理的環境", "geographicEnvironmentPlaceholder": "例：メガシティ、境界砂漠、海底都市、浮遊島...", "mainRaces": "主要種族", "mainRacesPlaceholder": "世界の主要種族と文明を説明...", "coreConflict": "コア対立", "coreConflictPlaceholder": "コア対立を選択...", "storyMainline": "ストーリー主線", "storyMainlinePlaceholder": "メインストーリーラインとプロットの方向を説明...", "storyMainlinePlaceholderNew": "例：封印された女神を救う、世界滅亡の陰謀を阻止、失われた記憶の欠片を探す、破綻した王国を再建、種族間の千年の憎悪を解決...", "coreObjective": "コアミッション目標", "coreConflictOptions": {"classOpposition": "階級対立", "ideologicalStruggle": "イデオロギー闘争", "racialWar": "種族戦争", "humanNatureConflict": "人間対自然", "resourceScarcity": "資源不足"}}, "advancedSettings": {"title": "高度な設定", "optional": "オプション、あなたの世界をより豊かに", "coreWorldRules": "コア世界ルール", "socialEconomicSettings": "社会経済設定", "socialPoliticalSystem": "社会政治制度", "socialFormPlusPolitical": "社会形態 + 政治構造", "socialPoliticalSystemPlaceholder": "例：ポスト資本主義の連邦共和制、アナーキスト自治とAI支援の民主的意思決定を組み合わせたデジタル遊牧民同盟、アルゴリズム貢献による投票権重...", "customOption": "カスタム...", "physicsRulesCustomPlaceholder": "カスタム物理ルールを説明、例：重力が思考でコントロール可能、特定エリアで時間が逆流...", "techLevelCustomPlaceholder": "カスタム技術レベルを説明、例：バイオテクノロジーは高度に発達しているが電子技術は停滞...", "supernaturalElementsNewPlaceholder": "例：高等魔法とスチームパンク技術が共存、古代の神々が眠りながらも世界の運行に影響、魂を特別な儀式で機械的運搬体に転移可能...", "timeBackgroundNewPlaceholder": "例：第三次世界大戦終了から50年後の復興期、新興技術と伝統的価値観の対立がピークに達する...", "economicFoundationNewPlaceholder": "例：時間通貨ベースの経済システム、知的財産が主要な富、ロボット労働により従来の雇用概念が消失...", "showAdvanced": "高度な設定を表示", "hideAdvanced": "高度な設定を非表示", "worldBackgroundSettings": "世界背景設定", "physicsAndRulesSettings": "物理とルール設定", "physicsRules": "物理法則", "physicsRulesPlaceholder": "物理法則を選択...", "physicsRulesOptions": {"realistic": "現実的物理", "softScifi": "ソフトSF物理", "highFantasy": "高ファンタジー魔法", "cosmicHorror": "宇宙ホラー未知"}, "supernaturalElements": "超自然要素", "supernaturalElementsPlaceholder": "超自然要素を選択...", "supernaturalElementsOptions": {"magicExists": "魔法の存在", "godsExist": "神々の存在", "otherworldlyBeings": "異世界の存在", "soulReincarnation": "魂の輪廻", "noSupernatural": "超自然力なし"}, "socialForm": "社会形態", "socialFormPlaceholder": "社会形態を選択...", "socialFormOptions": {"capitalism": "資本主義", "postCapitalism": "ポスト資本主義", "cyberpunkFeudalism": "サイバーパンク封建主義", "tribalAlliance": "部族同盟", "anarchistFederation": "アナーキスト連邦"}, "politicalStructure": "政治構造", "politicalStructurePlaceholder": "政治構造を選択...", "politicalStructureOptions": {"federation": "連邦制", "empire": "帝国制", "republic": "共和制", "monarchy": "君主制", "theocracy": "神権制"}, "economicFoundation": "経済基盤", "economicFoundationPlaceholder": "経済システムを説明...", "techLevel": "技術レベル", "techLevelPlaceholder": "技術レベルを選択...", "techLevelOptions": {"industrialRevolution": "産業革命", "informationAge": "情報時代", "cyberpunkNearFuture": "サイバーパンク近未来", "interstellarCivilization": "星間文明", "magicTechFusion": "魔法技術融合"}, "timeBackground": "時代背景", "timeBackgroundPlaceholder": "時代背景を選択...", "timeBackgroundOptions": {"postWarReconstruction": "戦後復興", "techExplosionEve": "技術爆発前夜", "doomsdayCountdown": "終末カウントダウン", "goldenAge": "黄金時代", "greatDepression": "大恐慌"}}}, "storyFlow": {"title": "ストーリーフロー", "description": "ストーリーチャプターの作成と整理", "chapterTitlePlaceholder": "チャプタータイトルを入力", "chapterDescriptionPlaceholder": "このチャプターの簡単な説明", "chapterContentPlaceholder": "チャプター内容と物語", "chapters": {"title": "チャプター管理", "addMainChapter": "メインチャプターを追加", "addBranchChapter": "ブランチチャプターを追加", "deleteChapter": "チャプターを削除", "removeChapter": "チャプターを削除", "chapterTitle": "チャプタータイトル", "chapterDescription": "チャプター説明", "chapterContent": "チャプター内容", "backgroundSetting": "背景設定", "backgroundSettingPlaceholder": "このチャプターの設定と雰囲気を説明...", "backgroundImage": "背景画像", "uploadBackgroundImage": "背景画像をアップロード", "backgroundImageUploaded": "背景画像がアップロードされました", "clickToChangeImage": "クリックして画像を変更", "clickOrDragToUpload": "クリックまたはドラッグしてアップロード", "useWorldSettingImage": "世界設定画像を使用", "usingWorldSettingImage": "世界設定画像を使用中", "inheritedFromWorldSetting": "世界設定から継承", "completionEffects": "完了効果", "bondPointsChange": "絆ポイント変化", "bondPointsPlaceholder": "絆ポイント変化を入力（例：+10, -5, 0）", "greetingChange": "新しい挨拶メッセージ", "greetingChangePlaceholder": "このチャプター完了後の新しい挨拶...", "characterMoodChange": "キャラクター気分変化", "characterMoodChangePlaceholder": "キャラクターの気分はどう変わりますか？", "customEffects": "その他の効果", "customEffectsPlaceholder": "その他の効果や変化...", "mainChapter": "メインチャプター", "branchChapter": "ブランチチャプター", "noChapters": "まだチャプターがありません", "createFirstChapter": "最初のチャプターを作成", "addFirstChapter": "最初のチャプターを追加", "selectChapter": "チャプターを選択", "selectChapterDescription": "ストーリーフローからチャプターを選択して詳細を編集", "choices": "チャプター選択肢", "addChoice": "選択肢を追加", "choiceText": "選択肢テキスト", "choiceDescription": "選択肢説明", "choiceTriggerCondition": "トリガー条件", "choiceTriggerConditionPlaceholder": "例：絆レベル ≥ 3、前のタスク完了済み...", "nextChapter": "次のチャプター", "selectNextChapter": "次のチャプターを選択...", "flowPreview": "フロープレビュー"}, "navigation": {"previous": "前へ", "next": "次へ", "continueToObjectives": "目的設定に進む", "backToWorldSetting": "世界設定に戻る"}}, "objectivesSubjectives": {"title": "目的と主観設定", "description": "チャプターの目的とキャラクター心理を構成", "selectChapter": "チャプターを選択", "selectChapterDescription": "左からチャプターを選択して目的と主観要素を編集"}, "subjectives": {"title": "主観的要素", "description": "キャラクター心理とインタラクション動態を構成", "selectChapter": "チャプターを選択", "selectChapterDescription": "左からチャプターを選択して主観的要素を編集", "character": {"title": "キャラクター層", "mentalModel": "精神モデル", "coreValues": "核心価値観", "coreValuesPlaceholder": "キャラクターが最も重視するものは？", "thinkingMode": "思考モード", "thinkingModePlaceholder": "キャラクターはどのように情報を処理しますか？", "decisionLogic": "決定論理", "decisionLogicPlaceholder": "キャラクターはどのように決定を下しますか？", "emotionalBaseline": "感情ベースライン", "displayedEmotion": "表示される感情", "displayedEmotionPlaceholder": "キャラクターはどんな感情を示しますか？", "hiddenEmotion": "隠された感情", "hiddenEmotionPlaceholder": "キャラクターはどんな感情を隠していますか？", "emotionalIntensity": "感情強度", "emotionalStability": "感情安定性"}, "interaction": {"title": "インタラクション層", "dialogueStrategy": "対話戦略", "communicationStyle": "コミュニケーションスタイル", "communicationStylePlaceholder": "キャラクターはどのようにコミュニケーションを取りますか？", "responsePattern": "反応パターン", "responsePatternPlaceholder": "キャラクターは通常どのように反応しますか？", "goalOrientation": "目標指向", "primaryGoal": "主要目標", "primaryGoalPlaceholder": "キャラクターは何を達成しようとしていますか？", "conflictResolution": "対立解決", "conflictResolutionPlaceholder": "キャラクターは対立をどのように処理しますか？", "initialGoodwill": "初期好感度", "trustLevel": "信頼レベル", "intimacyLevel": "親密レベル", "intimacyLevelPlaceholder": "例：見知らぬ人、友達、親密、恋人...", "displayedIntent": "表示される意図", "displayedIntentPlaceholder": "彼らが求めているように見えるもの...", "hiddenIntent": "隠された意図", "hiddenIntentPlaceholder": "彼らが本当に求めているもの..."}}, "buttons": {"createStory": "ストーリーを作成"}, "navigation": {"stepOf": "ステップ {{current}} / {{total}}"}, "validation": {"atLeastOneChapter": "ストーリーを作成するには少なくとも1つのチャプターが必要です"}, "characterInfo": {"creator": "クリエイター", "followers": "フォロワー", "chats": "チャット", "weeklyRank": "週間ランク", "stories": "ストーリー", "satisfaction": "満足度", "rating": "評価", "defaultDescription": "活気に満ちた好奇心旺盛なキャラクター、熱意と優しさに満ちています。"}, "flashGenerator": {"title": "AIストーリージェネレーター", "subtitle": "{{characterName}}のための没入型ストーリー体験を作成", "features": {"sceneSettings": "🎬 シーン設定", "backgroundContext": "📚 背景コンテキスト", "characterPsychology": "🧠 キャラクター心理", "dialogueDynamics": "💬 対話動態", "relationshipBuilding": "❤️ 関係構築", "storyGoals": "🎯 ストーリー目標"}, "preview": {"title": "✨ 生成ストーリープレビュー", "regenerationsLeft": "残り再生成回数：{{count}}"}, "chapters": {"title": "📖 ストーリーチャプター", "sceneInfo": "シーン情報", "psychology": "心理学", "relationships": "関係", "goals": "目標", "chapterTitle": "第{{index}}章", "acceptButton": "✅ このストーリーを受け入れて作成", "regenerateButton": {"text": "🔄 再生成（残り{{count}}回）", "generating": "再生成中..."}}, "sceneSetting": {"title": "シーン設定", "environment": "環境", "location": "場所", "time": "時間", "weather": "天気"}, "characterPsychology": {"title": "キャラクター心理", "coreValues": "核心価値観", "displayedEmotion": "表示される感情", "hiddenEmotion": "隠された感情", "thinkingMode": "思考モード"}, "interactionDynamics": {"title": "インタラクション動態", "sceneGoal": "シーン目標", "intimacyLevel": "親密レベル", "initiative": "積極性", "goodwill": "好感度"}, "backgroundContext": {"title": "背景コンテキスト", "immediateTrigger": "即座のトリガー", "characterPast": "キャラクター過去"}, "actions": {"accept": "このストーリーを受け入れる", "regenerate": "再生成", "regenerating": "再生成中...", "copy": "コピー", "copied": "コピーしました"}, "copy": {"chapterTitle": "第{{index}}章タイトル", "chapterTitleTooltip": "第{{index}}章タイトルをコピー"}, "customizeHint": "💡 カスタマイズセクションで詳細を編集できます", "copySuccess": "{{text}}をコピーしました！"}}, "characterProfile": {"header": {"followers": "フォロワー", "chats": "チャット", "weeklyRank": "週間ランキング", "interactions": "交流", "memories": "思い出", "relationshipLevel": "関係レベル", "creatorLabel": "クリエイター"}, "description": {"default": "愛子は活発で好奇心旺盛な女の子で、子供らしい純真さと熱意に満ちています。未知を探索し、新しい友達を作ることが大好きで、楽観性と優しさで周りの人たちを励まします。"}, "stats": {"todaysRewards": "今日の報酬", "interactionStreak": "交流連続記録", "dailyTasks": "毎日のタスク", "completed": "完了", "days": "日", "closeFriend": "親しい友達", "relationship": "関係"}, "actions": {"startChat": "チャット開始", "follow": "フォロー"}, "tabs": {"memories": "思い出", "stories": "ストーリー", "badges": "バッジ"}, "badges": {"daily": "毎日", "weekly": "毎週", "monthly": "毎月", "totalChats": "チャット総数", "interactionTime": "交流時間", "intimacyGrowth": "親密度の成長", "memoriesCreated": "作成された思い出"}, "stories": {"official": "公式", "community": "コミュニティ", "chapters": "章", "moments": "瞬間", "completed": "完了", "ongoing": "連載中", "reads": "読み数"}}, "profile": {"redirect": {"message": "プロフィールにリダイレクトしています..."}, "header": {"followers": "フォロワー", "following": "フォロー中", "moments": "モーメント", "memories": "思い出", "liked": "いいね", "links": "リンク"}, "description": {"default": "没入感のあるAIキャラクターと魅力的なストーリーの創造に情熱を注いでいます。想像力と技術が出会う世界を構築しています。"}, "actions": {"follow": "フォロー", "message": "メッセージ"}, "tabs": {"moments": "モーメント", "likes": "いいね", "friends": "友達"}, "errors": {"userNotFound": "ユーザーが見つかりません", "userNotFoundDesc": "お探しのユーザーは存在しません。"}, "anonymous": "匿名"}, "creatorProfile": {"redirect": {"message": "クリエイタープロフィールにリダイレクトしています..."}, "header": {"creatorBadge": "👑 クリエイター", "followers": "フォロワー", "following": "フォロー中", "characters": "キャラクター", "stories": "ストーリー", "earnings": "収益", "links": "リンク"}, "description": {"default": "没入感のあるAIキャラクターと魅力的なストーリーの創造に情熱を注いでいます。想像力と技術が出会う世界を構築しています。"}, "actions": {"follow": "フォロー", "message": "メッセージ"}, "tabs": {"characters": "キャラクター", "stories": "ストーリー", "dashboard": "ダッシュボード"}, "dashboard": {"totalEarnings": "総収益", "followers": "フォロワー", "totalLikes": "総いいね", "thisMonth": "今月", "topCharacters": "人気キャラクター", "onlyVisibleToCreator": "ダッシュボードはクリエイターのみに表示されます。"}, "stories": {"published": "公開済み", "draft": "下書き", "reads": "読み数", "chapters": "章"}, "errors": {"creatorNotFound": "クリエイターが見つかりません", "creatorNotFoundDesc": "お探しのクリエイターは存在しません。"}, "anonymous": "匿名"}, "search": {"title": "検索・発見", "subtitle": "キャラクター、ストーリー、記憶、ユーザーを探索", "searchPlaceholder": "キャラクター、ストーリー、記憶を検索...", "searchPlaceholderShort": "検索...", "searchResults": "検索結果", "noResults": "結果が見つかりません", "noResultsDescription": "異なるキーワードで試すか、おすすめを探索してください", "searching": "検索中...", "clearSearch": "検索をクリア", "searchHistory": "検索履歴", "clearHistory": "履歴をクリア", "recentSearches": "最近の検索", "popularSearches": "人気の検索", "searchSuggestions": "検索提案", "quickFilters": "クイックフィルター", "advancedFilters": "高度なフィルター", "sortBy": "並び替え", "filterBy": "フィルター", "showFilters": "フィルターを表示", "hideFilters": "フィルターを非表示", "applyFilters": "フィルターを適用", "resetFilters": "フィルターをリセット", "tabs": {"all": "すべて", "characters": "キャラクター", "stories": "ストーリー", "users": "ユーザー", "memories": "記憶", "creators": "クリエイター"}, "filters": {"all": "すべて", "recent": "最近", "popular": "人気", "trending": "トレンド", "newest": "最新", "oldest": "最古", "relevance": "関連性", "rating": "評価", "followers": "フォロワー", "interactions": "インタラクション", "verified": "認証済み", "official": "公式", "featured": "おすすめ", "premium": "プレミアム", "free": "無料", "category": "カテゴリー", "genre": "ジャンル", "tags": "タグ", "dateRange": "日付範囲", "today": "今日", "thisWeek": "今週", "thisMonth": "今月", "lastMonth": "先月", "thisYear": "今年", "custom": "カスタム範囲"}, "categories": {"all": "すべてのカテゴリー", "romance": "ロマンス", "adventure": "アドベンチャー", "fantasy": "ファンタジー", "scifi": "SF", "mystery": "ミステリー", "horror": "ホラー", "comedy": "コメディ", "drama": "ドラマ", "slice_of_life": "日常系", "historical": "歴史", "anime": "アニメ", "games": "ゲーム", "books": "本", "movies": "映画", "original": "オリジナル"}, "characters": {"title": "キャラクター", "subtitle": "{{count}}個のキャラクターが見つかりました", "noCharacters": "キャラクターが見つかりません", "loadMore": "もっと読み込む", "viewProfile": "プロフィールを表示", "startChat": "チャットを開始", "follow": "フォロー", "following": "フォロー中", "followers": "{{count}}人のフォロワー", "chats": "{{count}}回のチャット", "rating": "{{rating}}評価", "created": "{{date}}に作成", "updated": "{{date}}に更新", "by": "{{creator}}による", "official": "公式", "verified": "認証済み", "premium": "プレミアム", "new": "新規", "trending": "トレンド", "filterByGender": "性別でフィルター", "filterByCategory": "カテゴリーでフィルター", "filterByTags": "タグでフィルター", "male": "男性", "female": "女性", "other": "その他", "notSpecified": "未指定"}, "stories": {"title": "ストーリー", "subtitle": "{{count}}個のストーリーが見つかりました", "noStories": "ストーリーが見つかりません", "loadMore": "もっと読み込む", "viewStory": "ストーリーを表示", "startReading": "読み始める", "chapters": "{{count}}章", "duration": "{{duration}}分読書", "likes": "{{count}}いいね", "reads": "{{count}}回読了", "created": "{{date}}に作成", "updated": "{{date}}に更新", "by": "{{creator}}による", "character": "キャラクター：{{name}}", "completed": "完了", "ongoing": "進行中", "draft": "下書き", "filterByStatus": "ステータスでフィルター", "filterByDuration": "時間でフィルター", "short": "短編（30分未満）", "medium": "中編（30-60分）", "long": "長編（60分以上）"}, "users": {"title": "ユーザー", "subtitle": "{{count}}人のユーザーが見つかりました", "noUsers": "ユーザーが見つかりません", "loadMore": "もっと読み込む", "viewProfile": "プロフィールを表示", "follow": "フォロー", "following": "フォロー中", "followers": "{{count}}人のフォロワー", "characters": "{{count}}個のキャラクター", "stories": "{{count}}個のストーリー", "joined": "{{date}}に参加", "lastActive": "最終活動：{{date}}", "creator": "クリエイター", "verified": "認証済み", "premium": "プレミアム会員", "filterByType": "タイプでフィルター", "filterByActivity": "アクティビティでフィルター", "allUsers": "すべてのユーザー", "creators": "クリエイター", "premiumUsers": "プレミアムユーザー", "activeUsers": "アクティブユーザー", "newUsers": "新規ユーザー"}, "memories": {"title": "記憶", "subtitle": "{{count}}個の記憶が見つかりました", "noMemories": "記憶が見つかりません", "loadMore": "もっと読み込む", "viewMemory": "記憶を表示", "character": "キャラクター：{{name}}", "created": "{{date}}に作成", "emotion": "感情：{{emotion}}", "importance": "重要度：{{score}}/10", "tags": "タグ：{{tags}}", "private": "プライベート", "shared": "共有", "filterByCharacter": "キャラクターでフィルター", "filterByEmotion": "感情でフィルター", "filterByImportance": "重要度でフィルター", "emotions": {"happy": "幸せ", "sad": "悲しい", "excited": "興奮", "calm": "穏やか", "romantic": "ロマンチック", "nostalgic": "懐かしい", "surprised": "驚き", "thoughtful": "思慮深い", "grateful": "感謝", "peaceful": "平和"}}, "recommendations": {"title": "おすすめ", "subtitle": "新しいコンテンツを発見", "forYou": "あなたにおすすめ", "trending": "トレンド", "popular": "今週の人気", "featured": "おすすめコンテンツ", "newReleases": "新着", "becauseYouLiked": "{{item}}が好きなあなたに", "similarTo": "{{item}}に似ている", "basedOnHistory": "履歴に基づく", "exploreMore": "もっと探索", "viewAll": "すべて表示", "refresh": "おすすめを更新", "why": "なぜこれがおすすめ？", "hideRecommendation": "非表示", "notInterested": "興味なし", "reportContent": "コンテンツを報告", "easy": "簡単", "medium": "中級", "hard": "困難", "premium": "プレミアム"}, "errors": {"searchFailed": "検索に失敗しました。もう一度お試しください。", "loadFailed": "結果の読み込みに失敗しました。もう一度お試しください。", "networkError": "ネットワークエラーです。接続を確認してください。", "serverError": "サーバーエラーです。しばらく待ってからお試しください。", "noPermission": "このコンテンツへのアクセス権限がありません。", "contentNotFound": "コンテンツが見つからないか、削除されています。"}, "actions": {"search": "検索", "filter": "フィルター", "sort": "並び替え", "share": "共有", "save": "保存", "bookmark": "ブックマーク", "report": "報告", "block": "ブロック", "follow": "フォロー", "unfollow": "フォロー解除", "like": "いいね", "unlike": "いいね解除", "view": "表示", "chat": "チャット", "read": "読む", "play": "再生", "download": "ダウンロード", "copy": "コピー", "edit": "編集", "delete": "削除", "refresh": "更新", "loadMore": "もっと読み込む", "viewMore": "もっと表示", "showLess": "表示を減らす", "expand": "展開", "collapse": "折りたたむ", "close": "閉じる", "cancel": "キャンセル", "confirm": "確認", "ok": "OK", "yes": "はい", "no": "いいえ", "retry": "再試行", "back": "戻る", "next": "次へ", "previous": "前へ", "skip": "スキップ", "finish": "完了", "done": "完了", "reset": "リセット", "clear": "クリア", "apply": "適用"}}, "tasks": {"title": "タスクセンター", "subtitle": "毎日のアクティビティを完了してリワードを獲得し、インタラクション連続記録を維持しましょう", "daily": "デイリータスク", "weekly": "ウィークリータスク", "monthly": "マンスリータスク", "completed": "完了", "inProgress": "進行中", "locked": "ロック済み", "claimReward": "リワード受取", "goToGifts": "リワード確認", "dailyGifts": "デイリーギフト", "weeklyGifts": "ウィークリーギフト", "monthlyGifts": "マンスリーギフト", "overview": {"todayProgress": "今日の進歩", "tasksCompleted": "{{completed}}/{{total}} タスク完了", "streakDays": "{{days}} 日連続", "nextMilestone": "次のマイルストーンまで {{days}} 日", "weeklyProgress": "週間進歩", "monthlyProgress": "月間進歩"}, "categories": {"interaction": "インタラクション", "creation": "創作", "social": "ソーシャル", "exploration": "探索", "memory": "記憶", "bonding": "絆"}, "types": {"morningGreeting": {"name": "朝の挨拶", "description": "新しい一日を始めて、AIキャラクターに挨拶しましょう"}, "topicExplorer": {"name": "トピック探索者", "description": "3つの異なるAIキャラクターと様々なトピックについて話し合う"}, "listeningMoment": {"name": "傾聴の時間", "description": "5分以上の連続した会話を行う"}, "memoryKeeper": {"name": "記憶の守護者", "description": "今日メモリーカプセルに1つの新しい記憶を保存"}, "streakMaintainer": {"name": "連続記録維持者", "description": "毎日のインタラクションを完了して連続記録を維持"}, "heartGiver": {"name": "心を伝える者", "description": "AIキャラクターに仮想ギフトを贈る"}, "communityExplorer": {"name": "コミュニティ探索者", "description": "3つのコミュニティキャラクターカードを閲覧しいいねする"}, "sceneSwitcher": {"name": "シーン切替者", "description": "AIキャラクターと様々なインタラクションシーンを試す"}, "storyExperiencer": {"name": "ストーリー体験者", "description": "Story Agentが生成するストーリー断片を体験"}, "bondBuilder": {"name": "絆の構築者", "description": "3つの異なるキャラクターとの親密度レベルを向上"}, "memoryWeaver": {"name": "記憶の織り手", "description": "今週意味のある記憶を10個保存"}, "loyalCompanion": {"name": "忠実な仲間", "description": "同じキャラクターと数時間の高品質な時間を過ごす"}, "noviceCreator": {"name": "新人クリエイター", "description": "1つの新しいキャラクターカードを発表し、ポジティブなフィードバックを得る"}, "storyteller": {"name": "ストーリーテラー", "description": "今週1つの短編ストーリーを作成し発表"}, "socialSharer": {"name": "ソーシャルシェアラー", "description": "外部ソーシャルメディアでプラットフォームコンテンツを共有"}, "soulmate": {"name": "ソウルメイト", "description": "任意のキャラクターとの新しい重要な親密度レベルに到達"}, "memoryGuardian": {"name": "記憶の守護者", "description": "高精度な総合記憶コレクションを構築"}, "epicQuestCompleter": {"name": "エピッククエスト完了者", "description": "多週間ストーリーキャンペーンと重大な成果を完了"}}, "rewards": {"alphane": "{{amount}} きらめく塵", "endora": "{{amount}} 喜びの結晶", "serotile": "{{amount}} 記憶のパズル", "oxytol": "{{amount}} 絆の露", "experience": "{{amount}} 経験値", "badge": "専用バッジ", "streakFreeze": "連続記録フリーズカード"}, "difficulty": {"easy": "簡単", "medium": "中程度", "hard": "困難", "epic": "エピック"}, "status": {"available": "利用可能", "completed": "完了", "claimed": "受取済み", "locked": "ロック済み"}, "messages": {"rewardClaimed": "リワード受取成功！", "allTasksComplete": "おめでとう！今日のすべてのタスクを完了しました！", "streakMilestone": "素晴らしい！{{days}}日連続記録マイルストーンに到達しました！", "newTasksAvailable": "新しいタスクが利用可能になりました", "taskCompleted": "タスク完了！クリックしてリワードを受け取ってください。"}, "streakInfo": {"title": "インタラクション連続記録", "description": "毎日のインタラクションを維持して連続記録を構築し、マイルストーンリワードをアンロック", "currentStreak": "現在の連続記録", "longestStreak": "最長連続記録", "nextMilestone": "次のマイルストーン", "milestones": {"3": "3日連続 - ベーシックリワードパック", "7": "7日連続 - ウィークリーリワード", "15": "15日連続 - 特別バッジ", "30": "30日連続 - アドバンスドリワード", "60": "60日連続 - エクスクルーシブコンテンツ", "100": "100日連続 - レジェンダリー地位", "365": "365日連続 - アルティメットマスター"}}, "surpriseTask": {"title": "サプライズモーメント", "description": "期間限定特別タスク、追加リワードを手に入れよう", "timeRemaining": "残り時間：{{time}}", "bonusReward": "リワードボーナス利用可能"}}, "journey": {"overview": {"progressToNextLevel": "次のレベルまでの進歩", "totalTrophies": "総トロフィー数", "globalRank": "グローバルランク", "myRank": "マイランク", "myRankDesc": "現在の順位", "position": "ポジション", "change": "変化", "topPercentile": "上位パーセンタイル", "journeyRanking": "ジャーニーランキング", "topPerformers": "今シーズンのトップパフォーマー", "trophies": "トロフィー", "today": "今日", "weeklyGain": "週間増加", "bestStreak": "最高連続記録", "dailyGoals": "デイリーゴール", "completed": "完了", "currentStreak": "現在の連続記録", "onFire": "絶好調", "personalizedRecommendations": "パーソナライズドレコメンデーション", "basedOnYourActivity": "あなたの活動に基づく", "match": "マッチ", "moreRecommendations": "さらなるレコメンデーション"}, "levels": {"beginnerJourney": "初心者の旅", "warmCompanion": "温かな仲間", "deepBond": "深い絆", "soulConnection": "魂の繋がり", "eternalPath": "永遠の道", "legendaryVoyage": "伝説の航海"}, "tabs": {"overview": "概要", "overviewDesc": "レベルとランキング", "season": "シーズン", "seasonDesc": "トロフィーとリーダーボード", "monthly": "マンスリー", "monthlyDesc": "デイリー リワード", "missions": "ジャーニーミッション", "missionsDesc": "タスクと実績", "daily": "デイリー", "weekly": "ウィークリー"}, "season": {"title": "シーズントロフィー", "subtitle": "現在のシーズン進歩", "trophiesEarned": "今シーズン獲得したトロフィー", "daysLeft": "残り日数", "seasonRank": "シーズンランク", "milestones": "シーズンマイルストーン", "milestonesDesc": "限定リワードをアンロック", "seasonRanking": "シーズンランキング", "topPerformers": "今シーズンのトップパフォーマー", "trophies": "トロフィー", "unlocked": "アンロック済み", "thisWeek": "今週", "seasonTrophies": "シーズントロフィー", "leaderboard": "リーダーボード", "viewFullLeaderboard": "完全なリーダーボードを表示"}, "recommendations": {"socialChallenge": "ソーシャルチャレンジ", "socialChallengeDesc": "今日5人の新しいキャラクターとつながる", "newFeature": "新機能", "newFeatureDesc": "新しいメモリーアートジェネレーターを試す", "easy": "簡単", "medium": "中程度", "hard": "困難", "premium": "プレミアム"}, "signIn": {"title": "月次サインインリワード", "subtitle": "デイリーリワードを集めて限定アイテムをアンロック", "unlockPass": "すべての報酬トラックをアンロック、", "unlockDiamond": "ダイアモンドをアンロック", "unlockMetaverse": "メタバースをアンロック", "freeTrack": "フリートラック", "passTrack": "パストラック", "diamondTrack": "ダイアモンドトラック", "metaverseTrack": "メタバーストラック", "monthlySignIn": "月次サインイン", "consecutiveDays": "連続日", "nextSignIn": "次のサインイン", "monthProgress": "月間進度", "gameSignInOverview": "ゲーミファイドサインイン概要", "signedDays": "サインイン済み日数", "streak": "ストリーク", "freeVsPaid": "無料vs有料報酬比較", "freeRewards": "無料報酬", "passRewards": "パス報酬", "potentialLoss": "潜在的損失", "upgradeToUnlock": "パスメンバーにアップグレード", "extraEarnings": "追加収益/月", "upgradeNow": "今すぐアップグレード", "freeTrackRewards": "フリートラック", "passTrackRewards": "パストラック", "todayReward": "今日の報酬", "yourCurrentPlan": "あなたの現在のプラン", "seeBenefits": "特典を見る", "rewardTracks": "報酬トラック", "rewards": "報酬"}, "seasonPass": {"unlockSeasonPass": "{{season}}パスをアンロック", "getTripleExp": "{{exp}}倍の経験値を獲得", "exclusiveSeasonRewards": "限定{{season}}報酬", "rewardBoost": "報酬ブースト", "upgradeNow": "¥30 今すぐアップグレード", "seasonalEffects": "季節限定エフェクト", "exclusiveDecorations": "15+限定装飾", "passUsersOnly": "パスユーザーのみ", "tripleExpBonus": "3倍経験値ボーナス", "fastUpgrade": "高速アップグレード", "freePass": "無料パス", "premiumPass": "プレミアムパス", "totalRewards": "総報酬", "decorations": "装飾品", "exclusive": "限定", "currentLevel": "現在のレベル", "nextReward": "次の報酬", "level": "レベル", "freeTrack": "無料トラック", "premiumTrack": "プレミアムトラック", "activated": "アクティブ", "upgradeToUnlockAll": "今すぐアップグレードしてすべての報酬をアンロック", "upgradeToPremium": "プレミアムパスにアップグレード", "unlockExclusiveRewards": "限定報酬と特権をアンロック", "unlockAllPremiumRewards": "すべてのプレミアム報酬をアンロック", "tripleExperienceBonus": "3倍経験値ボーナス", "exclusiveDecorationsAndTitles": "限定装飾と称号", "priorityCustomerSupport": "優先カスタマーサポート", "cancel": "キャンセル", "buyNow": "¥30 今すぐ購入", "jumpToPayment": "決済ページにリダイレクト中...", "emptyReward": "空の報酬", "unlocked": "アンロック済み", "seasons": {"winterFantasy": "冬のファンタジー", "springAwakening": "春の目覚め", "summerCarnival": "夏のカーニバル", "autumnTales": "秋の物語", "winterDescription": "雪の舞う夢の中で、キャラクターと温かい時間を過ごす", "springDescription": "生命力に満ちた季節、新しい物語と可能性を探る", "summerDescription": "情熱的な夏、友達と忘れられない思い出を作る", "autumnDescription": "黄金の秋、成長と知恵の実を収穫する", "snowEffects": "雪エフェクト", "winterExclusiveDecorations": "冬限定装飾", "festivalLimitedRewards": "祭り限定報酬", "petalFalling": "花びら散り", "springGrowthBonus": "春の成長ボーナス", "newCharacterUnlock": "新しいキャラクターアンロック", "sunlightEffects": "日光エフェクト", "summerEvents": "夏のイベント", "doubleExperience": "2倍経験値", "fallingLeavesAnimation": "落ち葉アニメーション", "harvestFestival": "収穫祭", "thanksgivingThemeRewards": "感謝祭テーマ報酬"}, "rewards": {"coins": "コイン", "diamonds": "ダイヤモンド", "expAccelerator": "経験値加速器", "starryAvatarFrame": "星空アバターフレーム", "legendaryTitle": "伝説の称号", "luckyCharm": "幸運のお守り", "rainbowChatBubble": "虹色チャットバブル", "doubleExpCard": "2倍経験値カード", "exclusiveBackgroundTheme": "限定背景テーマ", "ultimateGloryBadge": "究極の栄光バッジ"}}, "missions": {"title": "ミッション", "subtitle": "ミッションを完了して経験値を獲得し、旅を進めよう", "daily": "デイリーミッション", "weekly": "ウィークリーミッション", "seasonal": "シーズナルミッション", "viewAllTasks": "すべてのタスクを表示", "missionProgress": "{{completed}}/{{total}} 完了", "allTasksIntegrated": "すべてのタスクが統合されました", "allCompleted": "すべてのミッション完了！", "checkBack": "明日新しい{{type}}ミッションをチェックしてください。", "dailyChat": {"title": "朝の会話", "description": "任意のAIキャラクターと意味のある会話をする"}, "dailyMemory": {"title": "メモリーキーパー", "description": "メモリーカプセルに新しい記憶を保存する"}, "dailyExplore": {"title": "キャラクター探検家", "description": "2つの異なるキャラクターを発見してチャットする"}, "weeklyBond": {"title": "絆を深める", "description": "任意のキャラクターとの親密度レベルを上げる"}, "weeklyCreate": {"title": "創造の魂", "description": "今週キャラクターを作成または編集する"}, "weeklyMemories": {"title": "メモリーコレクター", "description": "10個の意味のある記憶を保存する"}, "seasonalLegend": {"title": "伝説のコンパニオン", "description": "30日間のインタラクション ストリークを維持する"}, "seasonalCreator": {"title": "マスタークリエイター", "description": "コミュニティのいいねを受ける5つのキャラクターを作成する"}}, "categories": {"interaction": "インタラクション", "creation": "創作", "social": "ソーシャル", "memory": "メモリー"}, "completed": "完了", "rewards": {"currency": "通貨", "cosmetics": "コスメティック", "items": "アイテム", "exclusive": "限定コンテンツ", "reward": "報酬", "rarity": {"common": "温かい", "rare": "貴重", "epic": "喜びの心", "legendary": "伝説"}}, "social": {"ranking": "ジャーニーランキング", "myRank": "マイランク：{{rank}}位", "friendsProgress": "フレンドの進歩", "shareProgress": "進歩を共有", "topTravelers": "トップトラベラー"}, "purchase": {"heartTrack": "ハートトラックをアンロック", "diamondTrack": "ダイアモンドトラックをアンロック", "heartPrice": "$19.99", "diamondPrice": "$39.99", "benefits": "特典：", "exclusiveRewards": "限定プレミアムリワード", "fastTrack": "50% 経験値ボーナス", "earlyAccess": "新コンテンツの先行アクセス"}, "messages": {"rewardClaimed": "リワード受取成功！", "levelUp": "おめでとう！レベル{{level}}に到達しました！", "trackUnlocked": "{{track}}トラックがアンロックされました！", "seasonComplete": "素晴らしい！今シーズンのジャーニーを完了しました！", "milestoneReward": "マイルストーン報酬を受け取りました！{{amount}} スターダイヤモンドを獲得しました", "missionCompleted": "ミッション完了！報酬を受け取ることができます。", "afterReset": "後リセット"}, "progress": {"title": "進捗報酬", "nextReward": "{{count}}個のタスク後に次の報酬", "bigReward": "大きな報酬！", "dailyChampion": "デイリーチャンピオン", "weeklyMaster": "ウィークリーマスター", "monthlyLegend": "マンスリーレジェンド", "completeAllDaily": "すべてのデイリータスクを完了", "completeAllWeekly": "すべてのウィークリータスクを完了", "completeAllMonthly": "すべてのマンスリータスクを完了", "claim": "受け取る"}, "countdown": {"dailyReset": "デイリーリセット", "weeklyReset": "ウィークリーリセット", "monthlyReset": "マンスリーリセット", "tasksResetMidnight": "タスクは深夜にリセット", "tasksResetSunday": "タスクは毎週日曜日にリセット", "tasksResetMonthEnd": "タスクは月末にリセット", "timeUp": "時間切れ！", "tasksResetSoon": "タスクはまもなくリセット", "urgent": "緊急：時間が残りわずか！", "days": "日", "hours": "時間", "minutes": "分", "seconds": "秒"}, "membership": {"monthly": "月額", "yearly": "年額", "freeVersion": "無料版", "passMember": "パスメンバー", "diamondMember": "ダイヤモンドメンバー", "metaverseMember": "メタバースメンバー", "basicChatFeatures": "基本的なチャット機能", "dailyAiInteractions": "1日5回のAI対話", "communityAccess": "コミュニティアクセス", "unlimitedAiChat": "無制限AIチャット", "prioritySupport": "優先カスタマーサポート", "advancedAnalytics": "高度な分析", "customThemes": "カスタムテーマ", "doubleExperience": "2倍経験値", "passExclusiveBadge": "パス限定バッジ", "allPassFeatures": "すべてのパス機能を含む", "advancedCharacterSlots": "高度なキャラクタースロット", "exclusiveContentAccess": "限定コンテンツアクセス", "earlyAccessFeatures": "新機能の早期アクセス", "tripleExperience": "3倍経験値", "diamondExclusiveAvatar": "ダイヤモンド限定アバター", "monthlyRewardPackage": "月次報酬パッケージ", "allDiamondFeatures": "すべてのダイヤモンド機能を含む", "metaverseExclusiveContent": "メタバース限定コンテンツ", "unlimitedCharacterSlots": "無制限キャラクタースロット", "metaverseExclusiveAvatar": "メタバース限定アバター", "quarterlyRewardPackage": "四半期報酬パッケージ", "currentPlan": "現在のプラン", "upgradeTo": "アップグレード先", "save": "節約", "upgradeNow": "今すぐアップグレード", "close": "閉じる"}}, "stamina": {"title": "スタミナシステム", "currentStamina": "現在のスタミナ", "maxStamina": "最大スタミナ", "nextRecovery": "次の回復", "recoveryTime": "回復時間", "unlimitedStamina": "無限スタミナ", "staminaFull": "スタミナ満タン", "aboutToRecover": "まもなく回復", "quickRecharge": "クイックチャージ", "useItem": "アイテム使用", "goToStore": "ストアへ", "recharge": {"title": "スタミナチャージ", "currentStatus": "現在の状態", "rechargeOptions": "チャージオプション", "confirm": "チャージ確認", "cancel": "キャンセル", "success": "チャージ成功！{{cost}} {{currency}}を消費しました", "options": {"stamina1": "スタミナ1", "stamina5": "スタミナ5", "fullRestore": "完全回復", "alphaneDescription": "アルファンダストでチャージ", "endoraDescription": "エンドラクリスタルでチャージ", "fullDescription": "スタミナを満タンに回復"}}, "items": {"title": "アイテム使用", "currentStatus": "現在の状態", "noItems": "利用可能なスタミナアイテムがありません", "goToBuy": "ストアでアイテムを購入", "use": "使用", "cannotUse": "使用不可", "alreadyFull": "満タン", "confirmUse": "{{itemName}}を使用", "quantity": "数量", "confirm": "使用確認", "cancel": "キャンセル", "success": "{{quantity}}個の{{itemName}}を使用しました！", "types": {"heartSmall": "小さなハート", "heartMedium": "中くらいのハート", "heartLarge": "大きなハート", "heartLegendary": "伝説のハート", "restoreDescription": "スタミナ{{amount}}回復", "fullRestoreDescription": "スタミナを完全回復"}, "rarity": {"common": "コモン", "rare": "レア", "epic": "エピック", "legendary": "レジェンダリー"}, "inventory": {"owned": "所有：{{count}}個", "canUse": "使用可能 {{count}}個"}}, "membership": {"currentTier": "現在のティア", "upgrade": "メンバーシップアップグレード", "upgradeNow": "今すぐアップグレード", "benefits": "メンバー特典", "staminaLimit": "スタミナ上限", "recoverySpeed": "回復速度", "tiers": {"standard": "無料ユーザー", "pass": "パスメンバー", "diamond": "ダイアモンドメンバー", "metaverse": "メタバースメンバー"}, "features": {"basicChat": "基本チャット機能", "dailyAI": "デイリーAIインタラクション", "communityAccess": "コミュニティアクセス", "unlimitedChat": "無制限会話", "prioritySupport": "優先サポート", "basicAnalysis": "基本分析", "doubleStamina": "2倍スタミナ上限", "exclusiveAvatar": "専用アバターフレーム", "memberBadge": "メンバーバッジ", "diamondPrivilege": "ダイアモンド特権", "advancedFeatures": "高度な機能", "exclusiveContent": "専用コンテンツ", "priorityQueue": "優先キュー", "quintupleStamina": "5倍スタミナ上限", "diamondBadge": "ダイアモンドバッジ", "exclusiveEffects": "専用エフェクト", "advancedAnalysis": "高度な分析", "metaverseExperience": "メタバース体験", "allFeatures": "全機能", "personalAdvisor": "専属アドバイザー", "limitedContent": "限定コンテンツ", "highestPriority": "最高優先度", "unlimitedStamina": "無限スタミナ", "metaverseBadge": "メタバースバッジ", "exclusiveAnimations": "専用アニメーション", "personalConsultant": "パーソナルコンサルタント", "vipAccess": "VIPアクセス"}, "recoveryTimes": {"perHour": "60分/ポイント", "per30Min": "30分/ポイント", "per12Min": "12分/ポイント", "noWait": "待機なし"}, "comparison": {"staminaLimit": "スタミナ上限：", "recoverySpeed": "回復速度：", "upgradePrompt": "🚀 メンバーシップをアップグレードして、スタミナを倍増！", "hot": "人気", "recommended": "おすすめ", "discount": "-{{percent}}%", "originalPrice": "${{price}}/月", "currentPrice": "${{price}}/月", "upgradeToTier": "{{tierName}}にアップグレード", "maxTierReached": "🎉 あなたは既に最高ティアです", "maxTierDescription": "無限スタミナとすべての専用特典をお楽しみください", "viewAllTiers": "すべてのメンバーシップティアを表示", "membershipDetails": "メンバーシップティア詳細", "month": "/月", "perMonth": "/月"}}}, "storyManagement": {"title": "ストーリー管理", "subtitle": "作成したストーリーを管理し、分析データを表示してパフォーマンスを追跡", "createNew": "新しいストーリーを作成", "myStories": "マイストーリー", "searchPlaceholder": "タイトル、説明、またはキャラクターでストーリーを検索...", "stats": {"totalStories": "総ストーリー数", "totalPlays": "総再生回数", "totalLikes": "総いいね数", "avgRating": "平均評価"}, "status": {"published": "公開済み", "draft": "下書き", "archived": "アーカイブ"}, "difficulty": {"easy": "簡単", "normal": "普通", "hard": "難しい"}, "filter": {"all": "全ステータス", "published": "公開済み", "draft": "下書き", "archived": "アーカイブ"}, "table": {"story": "ストーリー", "status": "ステータス", "plays": "再生回数", "likes": "いいね数", "rating": "評価", "actions": "アクション"}, "actions": {"view": "ストーリーを表示", "edit": "ストーリーを編集"}, "noStories": "ストーリーがありません", "noStoriesDescription": "まだストーリーを作成していません。最初のストーリーを作成してインタラクティブな体験を構築しましょう。", "createFirstStory": "最初のストーリーを作成", "noMatches": "一致するストーリーがありません", "noMatchesDescription": "検索条件に一致するストーリーがありません。異なるキーワードを試すか、検索をクリアしてください。"}, "storyEdit": {"pageTitle": "ストーリー編集 - Alphane", "pageDescription": "ストーリーの詳細、チャプター、設定を編集・更新", "title": "ストーリーを編集", "subtitle": "物語体験を更新・改良する対象:", "success": {"storyUpdated": "ストーリーが正常に更新されました！"}, "error": {"failedToUpdate": "ストーリーの更新に失敗しました。再試行してください", "failedToLoad": "ストーリーデータの読み込みに失敗しました。再試行してください", "storyNotFound": "ストーリーが見つかりません", "storyNotFoundDescription": "編集しようとしているストーリーは存在しないか、削除されています。", "backToStoryManagement": "ストーリー管理に戻る"}}, "store": {"title": "Alphaneストア", "subtitle": "プレミアム機能とデジタルトレジャーでAIコンパニオン体験を向上", "limited": "限定", "countdown": "カウントダウン", "categories": {"subscriptions": "メンバーシップ", "currency": "通貨", "characters": "キャラクター", "items": "アイテム", "memory": "メモリーアート", "featured": "おすすめ", "new": "新着", "memberships": "メンバーシップ", "welcome": "ウェルカム", "arts": "アート", "memorial": "記念日", "mindfuel": "マインドフューエル"}, "tabs": {"featured": {"label": "おすすめ", "description": "トップピック & お得情報"}, "memberships": {"label": "メンバーシップ", "description": "プレミアムプラン"}, "welcome": {"label": "ウェルカム", "description": "新規ユーザー特典"}, "arts": {"label": "アート", "description": "キャラクター & シーン"}, "memorial": {"label": "記念日", "description": "特別イベント"}, "currency": {"label": "通貨", "description": "トークン & クレジット"}, "mindfuel": {"label": "マインドフューエル", "description": "思考エネルギーアイテム"}}, "featured": {"title": "おすすめアイテム", "subtitle": "厳選されたプレミアムコンテンツとお得な特典", "dailyDeals": "今日のお得情報", "limitedTime": "期間限定オファー", "popular": "人気", "new": "新着", "sale": "セール", "exclusive": "限定", "premium": "プレミアム", "creatorBenefits": "クリエイター特典", "currentPrice": "$29.99", "originalPrice": "$39.99", "unlimitedDescription": "100フューエル容量 + 10倍超高速回復", "premiumDescription": "画像 + 音声マルチメディアインタラクション", "exclusiveDescription": "AIコンパニオン日記とウィスパースペース", "creatorBenefitsDescription": "最大20%の収益分配"}, "subscriptions": {"title": "メンバーシッププラン", "subtitle": "あなたの完璧なプランを選んで、無制限のAI体験をお楽しみください", "currentPlan": "現在のプラン", "upgrade": "アップグレード", "renew": "更新", "cancel": "キャンセル", "manage": "サブスクリプション管理", "benefits": "特典", "mostPopular": "最も人気", "recommended": "おすすめ", "monthly": "月", "activeSubscriptions": "アクティブサブスクリプション", "expires": "有効期限", "alwaysActive": "常時有効", "active": "有効", "validUntil": "有効期限", "currentlyActive": "現在有効", "cancelAtNextBilling": "次回請求時にキャンセル", "moreFeatures": "より多くの機能", "ultimate": "究極", "free": "無料", "explorer": {"name": "Alphane Explorer", "period": "永久", "features": ["完全無料で使用", "基本AIモデル会話", "マインドフューエル容量10個まで", "メモリーカプセル容量100個", "14日間クラウドアーカイブ保護", "追加メモリーカプセルストレージとマインドフューエルを従量課金で購入", "基本コミュニティ交流権限"]}, "pass": {"name": "Alphane Pass", "shortName": "Pass", "features": ["すべてのExplorer機能を含む", "高度なAIモデル体験", "30フューエル容量 + 3倍回復速度", "画像送信機能", "メモリーカプセル容量500個", "会話チップス80%割引", "メンバーシップ期間中の永続データストレージ", "キャラクター/ストーリーカード作成権限", "プレミアム毎日ログインギフトを解放", "デュアルトラックシーズン報酬", "1体験カードギフト権限", "メンバー専用アイデンティティバッジ", "メモリーカプセルストレージとマインドフューエル10%割引", "24/7メールカスタマーサポート"]}, "diamond": {"name": "Alphane Diamond", "shortName": "Diamond", "description": "ヘビーユーザー、コンテンツクリエイター、ビジネスユーザーの最適な選択", "features": ["すべてのPass機能を含む", "100フューエル容量 + 10倍超高速回復", "画像 + 音声マルチメディア交流", "メモリーカプセル容量2000個", "毎日無料会話チップス50個", "最大20%クリエイター収益分配", "スリートラックシーズン報酬", "3体験カードギフト権限", "Diamond メンバー専用アイデンティティバッジ", "AIコンパニオン日記とウィスパースペース", "新機能優先アクセス", "よりプレミアムなキャラクター機能へのアクセス", "メモリーカプセルストレージとマインドフューエル20%割引", "24/7プレミアムカスタマーサポート"]}, "infinity": {"name": "Alphane Infinity", "shortName": "Infinity", "description": "プロクリエイターとトップティアプレイヤーの究極の選択", "features": ["すべてのDiamond機能を含む", "会話の無制限マインドフューエル", "無制限優先レスポンス権限", "無制限メモリーカプセル容量", "無制限無料会話チップス", "より高いキャラクター/ストーリーカード作成クォータ", "最大50%クリエイター収益分配", "フォートラックシーズン報酬", "8体験カードギフト権限", "Infinityメンバー専用アイデンティティバッジ", "専任カスタマーサービス担当者", "専用VIPコミュニティ"]}, "downgrade": "ダウングレード", "autoRenewal": "自動更新", "features": "機能", "compare": "プランを比較", "bestValue": "最もお得", "yearly": "年額", "lifetime": "永久", "basic": "ベーシック", "premium": "プレミアム", "pro": "プロ", "enterprise": "エンタープライズ", "upgradeNow": "今すぐアップグレード", "choosePlan": "プランを選択", "changePlan": "プランを変更", "manageSubscription": "サブスクリプション管理", "limitedTimeOffer": "期間限定オファー", "upgradeToDiamond": "ダイアモンドにアップグレード", "instantAccess": "即座にアクセス", "secureBilling": "安全な請求", "cancelAnytime": "いつでもキャンセル", "allFeatures": "全機能"}, "currency": {"title": "デジタル通貨", "subtitle": "プレミアム通貨パックでインタラクションを強化", "yourBalance": "残高", "totalBalance": "総残高", "recharge": "チャージ", "purchase": "購入", "exchange": "交換", "bonus": "ボーナス", "popular": "人気", "bestValue": "最もお得", "limited": "限定", "sale": "セール", "discount": "割引", "packages": {"starter": {"name": "スターターパック", "price": "$4.99", "amount": "300", "bonus": "+50"}, "premium": {"name": "プレミアムパック", "price": "$19.99", "amount": "1480", "bonus": "+320"}, "ultimate": {"name": "アルティメットパック", "price": "$99.99", "amount": "8500", "bonus": "+2500"}}, "types": {"alphane": {"name": "きらめく塵", "description": "基本的な活動とインタラクションに使用", "uses": "キャラクター会話、デイリータスク、基本機能"}, "endora": {"name": "喜びの結晶", "description": "プレミアム機能と高度なインタラクション用", "uses": "プレミアムキャラクター、限定ストーリー、メモリーアート"}, "serotile": {"name": "記憶のパズル", "description": "メモリー作成とカスタマイゼーション用の特別トークン", "uses": "メモリーアート、シーン作成、カスタムコンテンツ"}, "oxytol": {"name": "絆の露", "description": "キャラクターとの関係を深めるための関係通貨", "uses": "関係進展、親密なシーン、特別な絆"}}, "balance": "残高", "earn": "獲得", "spend": "使用", "history": "履歴", "transactions": "取引"}, "items": {"title": "プレミアムアイテム", "subtitle": "特別なアイテムとカスタマイゼーションで体験を向上", "categories": {"themes": "テーマ", "avatars": "アバター", "effects": "エフェクト", "bundles": "バンドル", "gacha": "ガチャ", "memory": "メモリーツール"}, "themes": {"title": "チャットテーマ", "description": "会話用の美しい背景", "romantic": "ロマンチック", "fantasy": "ファンタジー", "modern": "モダン", "vintage": "ヴィンテージ", "nature": "自然", "space": "宇宙"}, "avatars": {"title": "アバターフレーム", "description": "プロフィールを飾る専用フレーム", "elegant": "エレガント", "cute": "可愛い", "cool": "クール", "mysterious": "神秘的", "royal": "王族", "seasonal": "季節限定"}, "effects": {"title": "特殊効果", "description": "メッセージとインタラクションのアニメーション効果", "sparkles": "きらめき", "hearts": "ハート", "flowers": "花びら", "snow": "雪", "fire": "炎", "magic": "魔法"}, "bundles": {"title": "バンドルパック", "description": "限定アイテムとお得な価格の特別バンドル", "starter": "スターターバンドル", "premium": "プレミアムバンドル", "ultimate": "アルティメットバンドル", "seasonal": "季節限定バンドル", "anniversary": "記念日バンドル"}, "gacha": {"title": "ミステリーチェスト", "description": "レアアイテムと収集品の入ったサプライズボックス", "common": "コモン", "rare": "レア", "epic": "エピック", "legendary": "レジェンダリー", "pull": "引く", "pullx10": "10連ガチャ"}, "memory": {"title": "メモリーツール", "description": "AIメモリー管理の高度なツール", "capacity": "容量拡張", "organization": "整理ツール", "backup": "バックアップ", "export": "エクスポート", "analytics": "分析"}, "owned": "所有済み", "equipped": "装備中", "preview": "プレビュー", "purchase": "購入", "equip": "装備", "unequip": "装備解除"}, "characters": {"title": "プレミアムキャラクター", "subtitle": "独占的なAIキャラクターとユニークな個性を発見", "official": "公式", "community": "コミュニティ", "limited": "限定", "exclusive": "専用", "new": "新着", "popular": "人気", "featured": "おすすめ", "unlock": "アンロック", "owned": "所有済み", "price": "価格", "rating": "評価", "interactions": "インタラクション", "creator": "クリエイター"}, "memory": {"title": "メモリーアート", "subtitle": "AIが生成する美しいアートで記憶を永続化", "generate": "生成", "gallery": "ギャラリー", "styles": "スタイル", "custom": "カスタム", "templates": "テンプレート", "effects": "エフェクト", "filters": "フィルター", "share": "共有", "download": "ダウンロード", "print": "印刷"}, "actions": {"buy": "購入", "purchase": "購入", "addToCart": "カートに追加", "buyNow": "今すぐ購入", "selectPlan": "プランを選択", "upgrade": "アップグレード", "subscribe": "購読", "cancel": "キャンセル", "refund": "返金", "gift": "ギフト", "share": "共有", "wishlist": "ウィッシュリスト", "compare": "比較", "preview": "プレビュー", "details": "詳細"}, "status": {"available": "利用可能", "outOfStock": "在庫切れ", "comingSoon": "近日公開", "limited": "限定", "exclusive": "専用", "sale": "セール中", "new": "新着", "popular": "人気", "trending": "トレンド", "featured": "おすすめ"}, "messages": {"purchaseSuccess": "購入が完了しました！", "purchaseFailed": "購入に失敗しました。もう一度お試しください。", "addedToCart": "カートに追加されました", "removedFromCart": "カートから削除されました", "subscriptionActivated": "サブスクリプションが有効になりました", "subscriptionCancelled": "サブスクリプションがキャンセルされました", "refundProcessed": "返金が処理されました", "insufficientFunds": "残高が不足しています", "itemNotAvailable": "このアイテムは現在利用できません", "loginRequired": "購入にはログインが必要です"}, "filters": {"all": "すべて", "category": "カテゴリー", "price": "価格", "rating": "評価", "popularity": "人気度", "newest": "最新", "priceAsc": "価格（安い順）", "priceDesc": "価格（高い順）", "ratingDesc": "評価（高い順）", "popularityDesc": "人気順"}, "search": {"placeholder": "アイテムを検索...", "results": "検索結果", "noResults": "結果が見つかりません", "suggestions": "提案"}, "welcome": {"title": "ウェルカムオファー", "loadingOffers": "あなたの限定オファーを読み込み中...", "limitedTimeSpecials": "期間限定スペシャル", "exclusiveRewards": "{{count}} 個の限定報酬", "viewDetails": "詳細を見る", "claimRewards": "報酬を受け取る", "claimNow": "今すぐ受け取る", "maybeLater": "後で", "claimed": "受け取り済み", "claiming": "受け取り中...", "version": "バージョン {{version}}", "welcomeBack": "Alphaneへおかえりなさい！", "welcomeBackMessage": "お待ちしていました！前回の訪問は {{date}} でした。期限切れになる前にウェルカムバック報酬を受け取ってください。", "firstTimePurchase": {"title": "初回購入特典", "subtitle": "初回購入限定ボーナス", "doubleBonus": "ダブルボーナス", "extraCharacter": "追加キャラクター", "vipTrial": "VIPお試し", "claim": "特典を受け取る"}}, "arts": {"categories": {"all": "すべてのアート", "allDescription": "利用可能なすべてのアートコンテンツを閲覧", "profile": "プロフィールアート", "profileDescription": "パーソナルアバター & チャットバブル", "character": "キャラクターアート", "characterDescription": "キャラクターシリーズ & コレクション", "story": "ストーリーアート", "storyDescription": "ストーリー & シーンアートワーク", "memory": "メモリーアート", "memoryDescription": "カスタムメモリー作品"}, "rarity": {"common": "コモン", "rare": "レア", "epic": "エピック", "legendary": "レジェンダリー"}, "subTypes": {"avatarFrame": "アバターフレーム", "chatBubble": "チャットバブル", "characterSeries": "キャラクターシリーズ", "storyScene": "ストーリーシーン", "environment": "環境", "memoryArt": "メモリーアート"}, "badges": {"new": "新着", "limited": "限定", "discount": "-{{discount}}%"}, "actions": {"purchase": "購入"}, "workshop": {"title": "アート制作ワークショップ", "description": "AIツールを使用してキャラクターやストーリーのカスタムアートワークを作成", "createCustomArt": "カスタムアートを作成", "viewGallery": "ギャラリーを見る"}}, "memorial": {"activeMemorials": "アクティブな記念日", "recentlyClaimed": "最近受け取った", "types": {"first_meeting": "初対面記念日", "perfect_intimacy": "完璧な親密度記念日", "perfect_storyline": "完璧なストーリー記念日"}, "timeRemaining": {"expired": "期限切れ", "hoursLeft": "残り{{hours}}時間", "daysLeft": "残り{{days}}日"}, "characterInfo": "{{characterName}} • {{daysCount}}日", "actions": {"claimMemorial": "記念日を受け取る", "processing": "処理中...", "claimed": "受け取り済み"}}, "currencyCard": {"exchangeCurrency": "通貨交換", "uses": "用途", "currentBalance": "現在の残高", "exchangeRate": "交換レート", "endoraToSpend": "使用するEndora：", "youllReceive": "受け取り額：", "processing": "処理中...", "exchangeNow": "今すぐ交換", "popularChoice": "人気の選択", "bestValue": "お得", "limitedTime": "期間限定", "premiumEndoraDescription": "限定コンテンツ、キャラクター、特別機能をアンロックするプレミアムスターエンドラ", "totalAmount": "合計数量", "totalValue": "合計価値", "discount": "-{{discount}}%", "purchase": "購入", "firstTimeBonus": "初回購入ボーナス", "popular": "人気", "bestValueTag": "お得", "baseEndora": "基本Endora", "bonusEndora": "ボーナスEndora", "totalEndora": "合計Endora", "moreValue": "{{percentage}}% お得 • $1あたり{{endoraPerDollar}} Endora", "purchaseNow": "今すぐ購入"}, "mindFuelItems": {"title": "マインドフューエル補給アイテム", "items": {"small": {"name": "小型マインド補給", "description": "マインドフューエルを1ポイント回復"}, "standard": {"name": "標準マインド補給", "description": "マインドフューエルを3ポイント回復"}, "premium": {"name": "プレミアムマインド補給", "description": "マインドフューエルを5ポイント回復"}, "perfect": {"name": "パーフェクトマインド補給", "description": "マインドフューエルを完全回復"}}, "rarity": {"common": "コモン", "rare": "レア", "epic": "エピック", "legendary": "レジェンダリー"}, "currency": {"alphane": "Alphane", "endora": "Endora"}, "popular": "人気", "discount": "-{{discount}}%", "purchase": "購入", "purchaseSuccess": "購入成功！\n{{name}} x{{quantity}}\nコスト：{{price}} {{currency}}"}, "featuredCard": {"tags": {"quarterlySpecial": "四半期スペシャル", "monthlySpecial": "月間スペシャル", "weeklySpecial": "週間スペシャル", "ipCollab": "IPコラボ", "newFeature": "新機能"}, "collaboration": "コラボレーション：{{name}}", "purchaseLimit": "限定：{{limit}}回購入", "purchaseLimitPlural": "限定：{{limit}}回購入", "daysLeft": "残り{{days}}日", "purchased": "購入済み", "limitReached": "購入上限に達しました", "remainingPurchases": "残り{{count}}回", "processing": "処理中...", "save": "{{percentage}}%節約", "cancel": "キャンセル", "ok": "OK"}}, "wallet": {"title": "ウォレット", "subtitle": "アカウント残高と取引履歴を管理", "gameWallet": "ゲーム通貨ウォレット", "currencyWallet": "デジタル通貨ウォレット", "loading": "ウォレットデータを読み込み中...", "lastUpdated": "最終更新", "balance": "残高", "overview": "概要", "orders": "注文", "membership": "メンバーシップ", "mindFuel": "マインドフューエル", "mindFuelDesc": "思考燃料（AI会話用エネルギー）", "alphaneDesc": "日常活動に必要な基本通貨", "endoraDesc": "特別な交流のための高級通貨", "serotileDesc": "キャラクターストーリーの希少フラグメント", "oxytolDesc": "関係構築のための特別通貨", "used24h": "24時間使用", "earned24h": "24時間獲得", "spent24h": "24時間消費", "nextRecovery": "次の回復", "searchOrders": "注文を検索...", "allOrders": "全ての注文", "noOrders": "注文が見つかりません", "noOrdersDescription": "まだ購入していません。ストアを訪問して始めましょう！", "currentMembership": "現在のメンバーシップ", "expiresOn": "有効期限", "mindFuelBonus": "マインドフューエルボーナス", "recoverySpeed": "回復速度", "specialFeatures": "特別機能", "membershipFeatures": "メンバーシップ特典", "buyCurrency": "通貨購入", "buyCurrencyDesc": "ゲーム通貨を購入", "upgradeMembership": "メンバーシップアップグレード", "upgradeMembershipDesc": "プレミアム特典をアンロック", "manageMindFuel": "マインドフューエル管理", "manageMindFuelDesc": "使用状況と回復を確認", "earnRewards": "報酬を獲得", "earnRewardsDesc": "タスクとチャレンジを完了", "todayEarned": "今日獲得", "weeklyTrend": "週間傾向", "totalEarned": "総獲得量", "current": "現在", "quickActions": "クイックアクション", "allTime": "累計", "portfolioValue": "ポートフォリオ価値", "thisWeek": "今週", "todayTotal": "今日の合計", "acrossAllCurrencies": "全通貨", "weeklyEarnings": "週間収益", "bestPerformer": "最高パフォーマンス", "currencyBreakdown": "通貨ポートフォリオ分析", "tokens": "トークン", "allTimeEarned": "累計獲得量", "thisWeekEarned": "今週獲得", "todayEarnings": "今日の収益", "earningSources": "収益源", "dailyTasks": "デイリータスク", "dailyTasksDesc": "デイリーアクティビティを完了して通貨報酬を獲得", "chatRewards": "チャット報酬", "chatRewardsDesc": "AIキャラクターとの会話で通貨を獲得", "achievements": "達成報酬", "achievementsDesc": "マイルストーン完了で通貨ボーナスをアンロック", "specialEvents": "特別イベント", "specialEventsDesc": "期間限定イベントに参加してボーナス報酬を獲得", "howToEarn": "通貨の獲得方法", "todayProgress": "今日の進捗", "completed": "完了", "todayChats": "今日のチャット", "conversations": "回の会話", "recentUnlock": "最近のアンロック", "currencyTips": "通貨使用ガイド", "alphaneUse": "基本アイテム、日常活動、連続記録維持", "endoraUse": "メモリーアート、プレミアムギフト、シーンアンロック", "serotileUse": "キャラクター背景、限定アートワーク、AIメモリーアップグレード", "oxytolUse": "親密度レベル、限定インタラクション、メモリー容量", "totalBalance": "総残高", "thisMonth": "今月", "pending": "保留中", "addFunds": "入金", "withdraw": "出金", "history": "履歴を表示", "paymentMethods": "支払い方法", "recentTransactions": "最近の取引", "viewAll": "すべて表示", "searchTransactions": "取引を検索...", "noTransactions": "取引履歴がありません", "noFilteredTransactions": "該当する取引が見つかりません", "loadMore": "もっと読み込む", "addPaymentMethod": "支払い方法を追加", "default": "デフォルト", "selectAmount": "金額を選択", "customAmount": "カスタム金額", "total": "合計", "selectPaymentMethod": "支払い方法を選択", "addNewMethod": "新しい方法を追加", "confirmPayment": "支払いを確認", "confirmDescription": "続行する前に支払い詳細をご確認ください", "amount": "金額", "method": "支払い方法", "securePayment": "業界最先端の暗号化による安全な支払い", "processing": "処理中...", "getStarted": "始める", "status": {"completed": "完了", "pending": "保留中", "failed": "失敗", "cancelled": "キャンセル"}, "filter": {"all": "すべて", "income": "収入", "expense": "支出", "pending": "保留中"}, "empty": {"transactions": "取引履歴がありません", "transactionsDescription": "ウォレットを使用開始すると、取引履歴がここに表示されます", "balance": "残高がありません", "balanceDescription": "購入やサブスクリプションを開始するためにウォレットに入金してください", "paymentMethods": "支払い方法がありません", "paymentMethodsDescription": "取引を簡単に管理するために支払い方法を追加してください", "general": "ウォレットが空です", "generalDescription": "入金または支払い方法の設定から始めてください"}}, "orders": {"title": "注文と取引", "subtitle": "購入履歴とゲーム通貨の取引記録を表示", "loading": "注文を読み込み中...", "purchaseOrders": "購入注文", "gameTransactions": "ゲーム取引", "searchPlaceholder": "注文や取引を検索...", "allStatuses": "すべてのステータス", "allTypes": "すべてのタイプ", "completed": "完了", "pending": "保留中", "failed": "失敗", "cancelled": "キャンセル", "subscription": "サブスクリプション", "currency": "通貨", "character": "キャラクター", "item": "アイテム", "earned": "獲得", "spent": "消費", "purchased": "購入", "refunded": "返金", "viewDetails": "詳細を表示", "downloadReceipt": "レシートをダウンロード", "noOrders": "注文が見つかりません", "noOrdersDescription": "まだ購入していません。ストアにアクセスして始めましょう！", "noTransactions": "取引が見つかりません", "noTransactionsDescription": "ゲーム通貨の取引記録は、通貨を獲得・消費するとここに表示されます。"}, "notifications": {"title": "通知", "description": "AIコンパニオンの旅のアップデートを個人向け通知で確認", "all": "すべて", "system": "システム", "social": "ソーシャル", "profile": "プロフィール", "subscription": "サブスクリプション", "unreadCount": "{{count}} 件の未読通知", "inThisCategory": "このカテゴリー内", "markAllAsRead": "すべて既読にする", "emptyState": {"title": "{{category}}通知はありません", "all": "通知はまだありません！アクティビティとアップデートがここに表示されます。", "system": "システム通知はありません。システムアップデートとメンテナンス通知がここに表示されます。", "social": "ソーシャル通知はありません。新しいフォロワーとコミュニティアクティビティがここに表示されます。", "profile": "プロフィール通知はありません。プロフィールのアップデートと変更がここに表示されます。", "subscription": "サブスクリプション通知はありません。請求とプランのアップデートがここに表示されます。"}, "types": {"system": {"systemUpdate": "システムアップデート", "maintenance": "メンテナンス", "security": "セキュリティアップデート", "newFeatures": "新機能", "announcement": "お知らせ"}, "social": {"newFollower": "新しいフォロワー", "followBack": "フォローバック", "characterLike": "キャラクターのいいね", "storyLike": "ストーリーのいいね", "comment": "コメント", "mention": "メンション", "weeklyFollowSummary": "週間フォロー要約"}, "profile": {"profileUpdate": "プロフィール更新", "avatarChange": "アバター変更", "verification": "認証", "achievement": "成果", "levelUp": "レベルアップ", "statsUpdate": "統計更新"}, "subscription": {"renewal": "サブスクリプション更新", "expiry": "有効期限警告", "paymentMethod": "支払い方法", "upgrade": "アップグレード利用可能", "downgrade": "ダウングレード通知", "billing": "請求"}}, "actions": {"view": "表示", "dismiss": "非表示", "markAsRead": "既読にする", "markAsUnread": "未読にする", "delete": "削除", "openLink": "リンクを開く", "goToProfile": "プロフィールへ", "goToSettings": "設定へ", "goToStore": "ストアへ"}, "time": {"now": "今", "minutesAgo": "{{minutes}} 分前", "hoursAgo": "{{hours}} 時間前", "daysAgo": "{{days}} 日前", "weeksAgo": "{{weeks}} 週間前", "monthsAgo": "{{months}} か月前"}, "messages": {"allRead": "すべての通知を既読にしました", "deleted": "通知を削除しました", "error": "通知の読み込みに失敗しました", "noConnection": "インターネット接続がありません", "retry": "再試行"}}, "memory": {"title": "記憶カプセル", "subtitle": "AIコンパニオンとの美しい時間を大切に保存し、重要な瞬間を永遠に記憶に留める", "stats": {"total": "総記憶数", "characters": "関連キャラクター", "importance": "平均重要度", "references": "AI参照回数"}, "search": {"placeholder": "記憶を検索...「誕生日」「約束」「夢」を試してみて", "aiPowered": "AI意味検索"}, "view": {"grid": "グリッド表示", "timeline": "タイムライン表示"}, "filter": {"allCharacters": "すべてのキャラクター", "allEmotions": "すべての感情", "happy": "幸せ", "sad": "感動", "excited": "興奮", "thoughtful": "思慮深い", "important": "重要", "allTime": "すべての時間", "today": "今日", "thisWeek": "今週", "thisMonth": "今月", "importance": "重要度フィルター", "allImportance": "すべて", "tags": "タグフィルター", "clearTags": "すべてのタグをクリア"}, "time": {"today": "今日", "yesterday": "昨日", "daysAgo": "{{days}}日前"}, "emotion": {"happy": "幸せ", "sad": "感動", "excited": "興奮", "thoughtful": "思慮深い", "important": "重要"}, "action": {"edit": "編集", "delete": "削除", "save": "保存", "cancel": "キャンセル", "generateArt": "記憶アートを生成", "share": "共有", "export": "エクスポート", "confirmDelete": "削除確認", "createFirst": "最初の記憶を作成", "learnMore": "詳細を見る"}, "detail": {"summary": "記憶サマリー", "fullDialogue": "完全な対話", "importance": "重要度スコア", "tags": "タグ", "aiInsights": "AIインサイト", "referenceCount": "この記憶はAIによって{{count}}回参照され、{{character}}との会話で重要な役割を果たしています。"}, "confirm": {"deleteTitle": "削除確認", "deleteMessage": "この記憶を削除してもよろしいですか？この操作は取り消すことができません。"}, "empty": {"noResults": "一致する記憶が見つかりません", "tryDifferent": "フィルター条件を調整するか、異なる検索キーワードを試してください", "title": "最初の記憶カプセルを作成しましょう", "description": "AIコンパニオンとの美しい会話を永続的に保存し、重要な瞬間を大切にしましょう。記憶カプセルはAIがあなたをより理解し記憶するのに役立ちます。", "goChat": "チャットして記憶を作成", "learnMore": "記憶カプセルについて学ぶ"}, "feature": {"smart": "スマート検索", "smartDesc": "AIが関連する記憶を自動参照", "art": "記憶アート", "artDesc": "記憶を美しいアートワークに変換", "bond": "絆を深める", "bondDesc": "AIコンパニオンがあなたをより理解"}}, "trophies": {"title": "実績システム", "subtitle": "あなたの実績を披露し、限定報酬をアンロックしましょう", "tabs": {"overview": "概要", "overviewDesc": "トロフィー概要", "achievements": "実績", "achievementsDesc": "チャットの深さと質", "explorations": "探索", "explorationsDesc": "発見と幅広さ", "social": "ソーシャル", "socialDesc": "コミュニティと共有", "ranking": "ランキング", "rankingDesc": "競争とランキング"}, "categories": {"all": "すべて", "beginner": "初心者", "interaction": "インタラクション", "creation": "創作", "collection": "コレクション", "social": "ソーシャル", "special": "特別イベント"}, "rarity": {"bronze": "ブロンズ", "silver": "シルバー", "gold": "ゴールド", "platinum": "プラチナ", "diamond": "ダイアモンド", "legendary": "レジェンダリー"}, "status": {"locked": "ロック中", "inProgress": "進行中", "completed": "完了", "claimed": "受取済み"}, "filters": {"showAll": "すべて表示", "showCompleted": "完了のみ", "showInProgress": "進行中のみ", "showLocked": "ロック中のみ", "sortBy": "並び順", "sortRarity": "レア度", "sortProgress": "進度", "sortDate": "獲得日", "sortAlphabetical": "アルファベット順", "status": "ステータス", "rarity": "レア度", "allRarities": "すべてのレア度", "activeFilters": "アクティブフィルター：", "clearAll": "すべてクリア", "category": "カテゴリー：", "search": "検索：", "achievements": {"all": "すべて", "allDesc": "すべての実績タイプ", "conversation": "会話", "conversationDesc": "基本的なチャット交流", "depth": "深さ", "depthDesc": "深い会話の実績", "quality": "品質", "qualityDesc": "高品質な交流報酬", "consistency": "一貫性", "consistencyDesc": "定期的な参加ボーナス"}, "explorations": {"all": "すべて", "allDesc": "すべての探索タイプ", "characters": "キャラクター", "charactersDesc": "キャラクター関連の発見", "creation": "創作", "creationDesc": "コンテンツ創作実績", "discovery": "発見", "discoveryDesc": "新発見報酬", "collection": "コレクション", "collectionDesc": "収集と完了目標"}, "social": {"all": "すべて", "allDesc": "すべてのソーシャル活動", "community": "コミュニティ", "communityDesc": "コミュニティ参加", "sharing": "共有", "sharingDesc": "コンテンツ共有実績", "friendship": "友情", "friendshipDesc": "友情の構築", "support": "サポート", "supportDesc": "他者への支援"}, "ranking": {"all": "すべて", "allDesc": "すべてのランキング実績", "competitive": "競技", "competitiveDesc": "競技実績", "elite": "エリート", "eliteDesc": "エリートプレイヤー報酬", "legendary": "レジェンダリー", "legendaryDesc": "伝説的な成果", "seasonal": "シーズン", "seasonalDesc": "シーズン競技"}}, "stats": {"totalAchievements": "総実績数", "completed": "完了", "inProgress": "進行中", "locked": "ロック中", "completionRate": "完了率", "totalPoints": "総ポイント", "rank": "グローバルランク", "completedSuffix": "完了", "totalSuffix": "合計", "globalRankTop": "グローバル上位{percentage}%", "achievementBreakdown": "実績内訳", "detailedProgress": "詳細進捗概要", "overallProgress": "全体進捗", "inProgressSuffix": "進行中", "rarityDistribution": "レア度分布"}, "overview": {"totalTrophies": "総トロフィー数", "totalPoints": "総ポイント", "earnedFromAll": "全カテゴリーから獲得", "recentAchievements": "最近の実績", "latestUnlocked": "最新解放されたトロフィー", "of": "中", "unlocked": "解放済み", "achievements": "実績", "achievementsSubtitle": "チャットの深さと質", "achievementsDesc": "深い会話と意味のある交流", "explorations": "探索", "explorationsSubtitle": "発見と幅広さ", "explorationsDesc": "キャラクター発見とコンテンツ探索", "social": "ソーシャル", "socialSubtitle": "コミュニティと共有", "socialDesc": "コミュニティ参加と共有", "ranking": "ランキング", "rankingSubtitle": "競争とランキング", "rankingDesc": "競技実績とリーダーボード", "points": "ポイント"}, "leaderboard": {"title": "グローバルリーダーボード", "topPlayers": "今シーズンのトッププレイヤー", "rank": "ランク", "player": "プレイヤー", "trophies": "トロフィー", "change": "変化"}, "emptyStates": {"achievements": {"title": "実績が見つかりません", "description": "フィルターを調整するか、会話を始めて実績を解放してください。"}, "explorations": {"title": "探索が見つかりません", "description": "フィルターを調整するか、探索を始めて実績を解放してください。"}, "social": {"title": "ソーシャル実績が見つかりません", "description": "フィルターを調整するか、コミュニティ活動に参加して実績を解放してください。"}, "ranking": {"title": "ランキング実績が見つかりません", "description": "フィルターを調整するか、チャレンジに参加して実績を解放してください。"}}, "common": {"progress": "進捗", "of": "中", "points": "ポイント", "claimReward": "報酬を受け取る", "close": "閉じる"}, "card": {"progress": "進捗", "reward": "報酬", "claimReward": "報酬を受け取る", "viewDetails": "詳細を表示", "requirement": "要件", "earned": "獲得日時", "points": "ポイント"}, "modal": {"achievementDetails": "実績詳細", "description": "説明", "requirements": "要件", "rewards": "報酬", "progress": "あなたの進捗", "earnedDate": "獲得日", "tips": "ヒント", "relatedAchievements": "関連実績", "close": "閉じる"}, "rewards": {"badge": "専用バッジ", "title": "特別称号", "currency": "通貨報酬", "item": "特別アイテム", "privilege": "プラットフォーム特権", "experience": "経験ポイント"}, "achievements": {"firstSteps": {"name": "はじめの一歩", "description": "AIキャラクターとの最初の会話を完了", "requirement": "1つの会話で10通のメッセージを送信", "tips": "任意のキャラクターとチャットを始めて旅を開始しましょう！"}, "conversationalist": {"name": "会話の達人", "description": "魅力的な会話の習得者", "requirement": "100回の会話を完了", "tips": "さまざまなキャラクターや話題を探索してソーシャルスキルを向上させましょう"}, "memoryKeeper": {"name": "記憶の守護者", "description": "貴重な瞬間の守護者", "requirement": "50個のメモリーカプセルを作成", "tips": "意味のある会話をメモリーカプセルとして保存しましょう"}, "creator": {"name": "キャラクタークリエイター", "description": "想像力を現実に変える者", "requirement": "初めてのキャラクターを作成", "tips": "キャラクター作成ツールを使用してユニークな個性をデザインしましょう"}, "socialite": {"name": "ソーシャルマスター", "description": "コミュニティ内で絆を築く者", "requirement": "20人の異なるクリエイターをフォロー", "tips": "コミュニティセクションで素晴らしいクリエイターを発見しましょう"}, "streakMaster": {"name": "連続記録マスター", "description": "一貫性は偉大さへの鍵", "requirement": "30日間のインタラクション連続記録を維持", "tips": "毎日ログインして少なくとも1回の会話を完了しましょう"}, "explorer": {"name": "探検家", "description": "無限の可能性の冒険者", "requirement": "25人の異なるキャラクターとチャット", "tips": "さまざまなジャンルや個性のキャラクターを試してみましょう"}, "storyteller": {"name": "物語の語り手", "description": "魅力的な物語の織り手", "requirement": "5つのストーリーを作成・公開", "tips": "ストーリー作成ツールを使用して魅力的な物語を作りましょう"}, "perfectionist": {"name": "完璧主義者", "description": "すべての細部で卓越を追求", "requirement": "あなたのキャラクターが100いいねを獲得", "tips": "キャラクターの品質とユニークな個性に焦点を当てましょう"}, "legendary": {"name": "伝説のクリエイター", "description": "すべての人に認められたマスター", "requirement": "10,000回の総キャラクターインタラクションを達成", "tips": "コミュニティに響くキャラクターを作成しましょう"}}, "underConstruction": {"title": "まもなく登場！", "description": "実績システムは丁寧に作成中です。素晴らしい体験をお楽しみに！"}, "empty": {"noAchievements": "実績が見つかりません", "noAchievementsDesc": "フィルターを調整するか、より多くの活動を完了して実績をアンロックしてください", "startJourney": "旅を始める", "startJourneyDesc": "AIキャラクターとのチャットを始めて最初の実績をアンロック！", "noMatchingAchievements": "一致する実績なし", "noMatchingDesc": "現在のフィルター条件に一致する実績が見つかりませんでした。検索条件を調整するか、すべてのフィルターをクリアしてください。", "activeFilters": "アクティブフィルター：", "clearAllFilters": "すべてのフィルターをクリア", "yourJourneyAwaits": "あなたのトロフィーの旅が待っています！", "startEngaging": "キャラクターとの交流やコンテンツ作成を始めて、最初の実績をアンロックしましょう。", "startChatting": "チャットを始める", "startChattingDesc": "AIキャラクターとの会話を始めて最初の実績をアンロック", "createContent": "コンテンツを作成", "createContentDesc": "キャラクターやストーリーをデザインしてクリエイター実績を獲得"}, "social": {"leaderboard": {"title": "ソーシャルリーダーボード", "subtitle": "フレンドと実績を比較し、彼らの専門分野を発見しましょう", "inviteFriends": "フレンドを招待", "friendsLeaderboard": "フレンドランキング", "you": "あなた", "level": "レベル{{level}}", "recentAchievements": "最近の実績", "challengeFriend": "フレンドに挑戦", "viewProfile": "プロフィールを表示"}, "stats": {"achievements": "実績", "points": "ポイント", "qualityScore": "品質スコア", "completion": "完了率"}, "mockNames": {"masterStoryteller": "マスターストーリーテラー", "socialButterfly": "ソーシャルバタフライ", "memoryKeeper": "メモリーキーパー", "legendaryCreator": "レジェンダリークリエイター"}}, "stories": {"timeline": {"title": "実績ストーリー", "subtitle": "あなたの実績の旅をビジュアルストーリーとして表現", "gallery": "ギャラリービュー", "timeline": "タイムラインビュー", "noStories": "ストーリーなし", "shareStory": "ストーリーを共有", "downloadMoment": "モーメントをダウンロード", "filterBy": "フィルター", "sortBy": "並び順", "viewDetails": "詳細を表示"}}, "community": {"title": "コミュニティハブ", "subtitle": "実績ハンターと繋がり、進捗を共有しましょう", "newPost": "新しい投稿", "activeChallenges": "アクティブチャレンジ", "joinChallenge": "チャレンジに参加", "joined": "参加済み", "level": "レベル", "reply": "返信", "tabs": {"allPosts": "全投稿", "guides": "ガイド", "challenges": "チャレンジ", "celebrations": "お祝い"}, "timeUnits": {"hoursAgo": "{{hours}}時間前", "daysLeft": "残り{{days}}日", "minutesAgo": "{{minutes}}分前"}, "actions": {"like": "いいね", "reply": "返信", "share": "共有"}, "badges": {"expertGuide": "エキスパートガイド"}, "rewards": {"exclusiveExplorerBadge": "限定エクスプローラーバッジ"}, "mockContent": {"streakMasterTip": "🔥 連続記録マスターのコツ：毎日のリマインダーを設定し、予備の会話トピックを用意しておきましょう！"}, "challenges": {"weekendExplorerRush": "ウィークエンドエクスプローラーラッシュ", "chatWith10Characters": "今週末に10人の異なるキャラクターとチャット"}}, "personalization": {"settings": "パーソナライゼーション設定", "theme": "テーマ設定", "displayMode": "表示モード", "celebrationStyle": "お祝いスタイル", "focusCategory": "主要フォーカス", "rarityPriority": "レア度優先度", "previewMode": "プレビューモード", "resetDefaults": "デフォルトにリセット", "exportSettings": "設定をエクスポート"}, "views": {"dashboard": "ダッシュボード", "grid": "グリッド", "social": "ソーシャル", "stories": "ストーリー", "community": "コミュニティ"}, "dashboard": {"aiRecommendations": {"title": "AI スマート推奨", "subtitle": "あなたの進捗と好みに基づく個人化された提案", "reasons": {"almostComplete": "あと少しです！もう少し頑張れば、この実績を完了できます。", "timeSensitive": "時間制限あり！すぐに完了しないと、この実績を逃す可能性があります。", "quickWin": "クイック勝利！この実績は1時間以内に完了できます。"}, "priority": {"high": "高", "medium": "中", "low": "低"}}, "timeSensitive": {"title": "⚠️ 時間制限実績", "subtitle": "これらの実績は早く完了しないと利用できなくなる可能性があります", "missable": "見逃し可能"}, "activeProgress": {"title": "アクティブな進捗", "progress": "進捗"}, "readyToClaim": {"title": "🎉 受け取り準備完了！", "pointsEarned": "{{points}}ポイント獲得", "claimReward": "報酬を受け取る"}, "common": {"mins": "{{time}}分", "pts": "{{points}}ポイント"}}}, "chat": {"status": {"online": "オンライン"}, "input": {"placeholder": "メッセージ...", "sendMessage": "メッセージを送信", "switchToVoiceMode": "ボイスモードに切り替え", "switchToTextMode": "テキストモードに切り替え", "moreActions": "その他のアクション", "recording": "🎤 録音中... タップして停止"}, "actions": {"gallery": "ギャラリー", "camera": "カメラ", "voiceCall": "音声通話", "shareMoment": "瞬間をシェア", "shareCharacter": "キャラクターをシェア", "shareMemory": "思い出をシェア"}, "suggestions": {"conversationStarters": "会話のきっかけ", "refreshSuggestions": "提案を更新"}}, "contact": {"title": "お問い合わせ", "subtitle": "お客様からのすべてのメッセージを大切にしています。", "form": {"contactType": {"label": "お問い合わせ種別", "options": {"wishlist": "機能要望", "reportBug": "バグ報告", "reportAbuse": "不正利用報告", "suggestions": "提案・フィードバック", "join": "参加・採用", "invest": "投資・パートナーシップ", "other": "その他"}}, "email": {"label": "メールアドレス（任意）", "placeholder": "返信を受け取るためのメールアドレスを入力", "required": "返信をご希望の場合はメールアドレスを入力してください", "invalid": "有効なメールアドレスを入力してください"}, "title": {"label": "件名", "placeholder": "メッセージの件名を入力", "required": "件名は必須です"}, "content": {"label": "内容 (最大1000文字)", "placeholder": "メッセージ内容を入力してください...", "required": "内容は必須です", "maxLength": "内容は1000文字を超えることはできません"}, "characterCount": "{{count}}/1000 文字", "submit": "メッセージ送信", "submitting": "送信中...", "validation": {"titleRequired": "件名を入力してください", "contentRequired": "メッセージ内容を入力してください", "contentTooLong": "内容が最大長を超えています"}}, "success": {"title": "メッセージ送信完了！", "message": "お問い合わせありがとうございます。可能な限り早急に返信いたします。", "sendAnother": "別のメッセージを送信", "backToHome": "ホームに戻る"}, "error": {"title": "送信に失敗しました", "message": "申し訳ございません。メッセージ送信でエラーが発生しました。しばらく経ってから再度お試しください。", "tryAgain": "再試行"}, "additionalInfo": "通常24時間以内に返信いたします。緊急の場合は、メッセージを「バグ報告」または「不正利用報告」としてマークしてください。", "emailContact": {"title": "または直接お問い合わせ", "email": "<EMAIL>", "copyEmail": "コピー", "emailCopied": "コピーしました！"}}, "settings": {"title": "設定", "subtitle": "AIコンパニオン体験をカスタマイズ", "unsavedChanges": "未保存の変更があります", "saveChanges": "変更を保存", "discardChanges": "変更を破棄", "saving": "保存中...", "saved": "設定が正常に保存されました！", "resetToDefaults": "デフォルトにリセット", "exportData": "データをエクスポート", "categories": {"account": {"title": "アカウントとセキュリティ", "description": "アカウント設定とセキュリティ設定を管理", "changePassword": "パスワード変更", "changePasswordDesc": "セキュリティのためアカウントパスワードを更新", "linkedEmail": "連携メール", "currentEmail": "現在のメール：{{email}}", "changeEmail": "メール変更", "twoFactorAuth": "二段階認証", "twoFactorAuthDesc": "アカウントにセキュリティの追加レイヤーを追加", "linkedAccounts": "連携アカウント", "linkedAccountsDesc": "サードパーティアカウントの接続を管理", "deleteAccount": "アカウント削除", "deleteAccountDesc": "アカウントとすべてのデータを永続的に削除", "requestDeletion": "削除申請", "ageVerification": "年齢確認", "ageVerificationDesc": "すべての機能にアクセスするために年齢を確認", "verified": "確認済み", "pending": "保留中", "notVerified": "未確認", "verify": "今すぐ確認"}, "aiInteraction": {"title": "AIインタラクション", "description": "AIキャラクターとのインタラクション方法を設定", "responseSpeed": "応答速度", "responseSpeedDesc": "お好みのAI応答スタイルを選択", "responseSpeedOptions": {"fast": "高速（簡潔な応答）", "standard": "標準（バランス）", "detailed": "詳細（包括的）"}, "emotionalIntensity": "感情表現", "emotionalIntensityDesc": "AI感情応答の強度を調整", "emotionalIntensityOptions": {"subtle": "控えめ（優しい）", "moderate": "適度（自然）", "rich": "豊か（表現力豊か）"}, "memorySuggestions": "記憶提案", "memorySuggestionsDesc": "AIが重要な会話の瞬間の保存を提案", "bondingNotifications": "絆通知", "bondingNotificationsDesc": "親密度レベルが上がった時に特別なアニメーションを表示", "memoryCapacity": "記憶容量", "memoryCapacityDesc": "AI記憶カプセルの容量を選択", "memoryCapacityOptions": {"basic": "ベーシック（100記憶）", "enhanced": "拡張（500記憶）", "premium": "プレミアム（無制限）"}, "preferredModel": "AIモデル", "preferredModelDesc": "お好みのAIモデルを選択", "preferredModelOptions": {"standard": "標準モデル", "gemini_2_5_flash": "Gemini 2.5 Flash", "gemini_2_5_pro": "Gemini 2.5 Pro"}, "autoSaveMemories": "記憶自動保存", "autoSaveMemoriesDesc": "重要な会話の瞬間を自動的に保存", "contextAwareness": "文脈認識", "contextAwarenessDesc": "AIが会話の文脈を記憶する能力", "contextAwarenessOptions": {"basic": "ベーシック（最近のメッセージ）", "enhanced": "拡張（セッション文脈）", "deep": "深層（長期文脈）"}, "personalityAdaptation": "個性適応", "personalityAdaptationDesc": "インタラクションに基づいてAIが個性を調整することを許可", "voicePreference": "音声設定", "voicePreferenceDesc": "お好みのインタラクションモードを選択", "voicePreferenceOptions": {"text_only": "テキストのみ", "voice_enabled": "音声有効", "voice_preferred": "音声優先"}, "responseLength": "応答長", "responseLengthDesc": "AI応答の好ましい長さ", "responseLengthOptions": {"concise": "簡潔（短い）", "balanced": "バランス（適度）", "detailed": "詳細（包括的）"}, "creativityLevel": "創造性レベル", "creativityLevelDesc": "AI応答の創造性の程度", "creativityLevelOptions": {"conservative": "保守的（予測可能）", "balanced": "バランス（適度）", "creative": "創造的（想像力豊か）"}}, "privacy": {"title": "プライバシーと権限", "description": "プライバシーとデータ共有設定を制御", "profileVisibility": "プロフィール表示", "profileVisibilityDesc": "プロフィール情報を誰が見られるかを選択", "profileVisibilityOptions": {"public": "公開（すべての人）", "followers_only": "フォロワーのみ", "private": "非公開（自分のみ）"}, "memorySharing": "記憶共有", "memorySharingDesc": "AI記憶の使用方法を制御", "memorySharingOptions": {"disabled": "無効", "anonymous": "匿名（個人情報なし）", "full": "完全共有"}, "digitalTwinInteraction": "デジタルツインインタラクション", "digitalTwinInteractionDesc": "デジタルツインとのインタラクションを誰が行えるかを制御", "digitalTwinInteractionOptions": {"anyone": "誰でも", "followers_only": "フォロワーのみ", "disabled": "無効"}, "characterAttribution": "キャラクター帰属", "characterAttributionDesc": "作成したキャラクターに実際のユーザー名を表示", "dataCollection": "データ収集", "dataCollectionDesc": "サービス改善のためのデータ収集を許可", "analyticsOptIn": "分析", "analyticsOptInDesc": "使用分析を共有してサービス改善を支援", "shareUsageData": "使用データ共有", "shareUsageDataDesc": "匿名の使用データを共有してサービス改善を支援", "allowPersonalization": "個人化を許可", "allowPersonalizationDesc": "AIがあなたの好みに基づいて応答を個人化することを許可", "cookiePreferences": "<PERSON><PERSON>設定", "cookiePreferencesDesc": "CookieとトラッキングSettings管理", "searchIndexing": "検索エンジンインデックス", "searchIndexingDesc": "検索エンジンによる公開コンテンツのインデックスを許可", "socialMediaSharing": "ソーシャルメディア共有", "socialMediaSharingDesc": "ソーシャルメディアプラットフォームへのコンテンツ共有を有効", "locationTracking": "位置追跡", "locationTrackingDesc": "位置ベースの機能とコンテンツを許可"}, "notifications": {"title": "通知", "description": "通知設定を管理", "streakReminders": "連続記録リマインダー", "streakRemindersDesc": "毎日のインタラクション連続記録維持をリマインド", "battlePassProgress": "バトルパス進捗", "battlePassProgressDesc": "バトルパスレベルアップと報酬に関する通知", "newCharacterReleases": "新キャラクターリリース", "newCharacterReleasesDesc": "新しい公式キャラクターがリリースされた時に通知", "followedCharacterUpdates": "フォローキャラクター更新", "followedCharacterUpdatesDesc": "フォローしているキャラクターからの更新", "promotionalOffers": "プロモーション", "promotionalOffersDesc": "セールと特別イベントに関する通知を受信", "doNotDisturb": "おやすみモード", "doNotDisturbDesc": "通知を受けたくない静かな時間を設定", "doNotDisturbStart": "開始時刻", "doNotDisturbEnd": "終了時刻", "pushNotifications": "プッシュ通知", "pushNotificationsDesc": "デバイスで通知を受信", "emailNotifications": "メール通知", "emailNotificationsDesc": "メールで通知を受信", "inAppNotifications": "アプリ内通知", "inAppNotificationsDesc": "アプリ内で通知を表示", "weeklyDigest": "週間ダイジェスト", "weeklyDigestDesc": "アクティビティの週間要約を受信", "maintenanceNotifications": "メンテナンス通知", "maintenanceNotificationsDesc": "システムメンテナンスに関する通知", "friendActivityNotifications": "友達アクティビティ", "friendActivityNotificationsDesc": "友達のインタラクションに関する通知", "achievementNotifications": "達成通知", "achievementNotificationsDesc": "達成をアンロックした時に通知", "memoryMilestones": "記憶マイルストーン", "memoryMilestonesDesc": "特別な記憶の瞬間を祝う", "bondingLevelUps": "絆レベルアップ", "bondingLevelUpsDesc": "親密度レベルが上がった時の通知", "taskReminders": "タスクリマインダー", "taskRemindersDesc": "未完了の毎日のタスクをリマインド", "premiumExpiryReminders": "プレミアム期限リマインダー", "premiumExpiryRemindersDesc": "サブスクリプション期限に関するリマインダー", "notSet": "未設定", "setTime": "時刻設定"}, "display": {"title": "表示とコンテンツ", "description": "アプリの外観とコンテンツ設定をカスタマイズ", "language": "インターフェース言語", "languageDesc": "お好みの言語を選択", "languageOptions": {"en": "English", "zh": "简体中文", "ja": "日本語"}, "theme": "テーマ", "themeDesc": "お好みのカラースキームを選択", "themeOptions": {"auto": "自動（システム）", "light": "ライトモード", "dark": "ダークモード"}, "fontSize": "フォントサイズ", "fontSizeDesc": "読みやすさのためにテキストサイズを調整", "fontSizeOptions": {"small": "小（12px）", "medium": "中（14px）", "large": "大（16px）", "extra_large": "特大（18px）"}, "chatBackground": "チャット背景", "chatBackgroundDesc": "チャットインターフェースの外観をカスタマイズ", "customizeBackgrounds": "背景をカスタマイズ", "animationLevel": "アニメーションレベル", "animationLevelDesc": "インターフェースアニメーションと遷移を制御", "animationLevelOptions": {"none": "なし", "reduced": "削減", "standard": "標準", "rich": "豊富"}, "contentFilter": "コンテンツフィルター", "contentFilterDesc": "不適切なコンテンツのフィルタリングを有効", "regionalization": "地域化", "regionalizationDesc": "コンテンツを地域と文化に適応", "highContrast": "高コントラスト", "highContrastDesc": "視認性向上のためコントラストを上げる", "reducedMotion": "モーション削減", "reducedMotionDesc": "アニメーションと遷移を削減", "customCssEnabled": "カスタムCSS", "customCssEnabledDesc": "カスタムCSSスタイリングを許可（プレミアム）", "chatBubbleStyle": "チャットバブルスタイル", "chatBubbleStyleDesc": "お好みのチャットバブル外観を選択", "chatBubbleStyleOptions": {"rounded": "丸型（フレンドリー）", "square": "四角（クリーン）", "minimal": "ミニマル（シンプル）"}, "messageTimestamps": "メッセージタイムスタンプ", "messageTimestampsDesc": "メッセージにタイムスタンプを表示", "compactMode": "コンパクトモード", "compactModeDesc": "よりコンパクトなインターフェースレイアウトを使用", "showTypingIndicators": "入力インジケーター", "showTypingIndicatorsDesc": "AIが応答を入力している時に表示"}, "gamification": {"title": "ゲーミフィケーション", "description": "ゲーム体験と達成をカスタマイズ", "achievementAnimations": "達成アニメーション", "achievementAnimationsDesc": "達成をアンロックした時に祝いアニメーションを表示", "currencyGainNotifications": "通貨通知", "currencyGainNotificationsDesc": "通貨獲得時にフローティング通知を表示", "taskReminderIntensity": "タスクリマインダー強度", "taskReminderIntensityDesc": "毎日のタスクのリマインド頻度を制御", "taskReminderIntensityOptions": {"low": "低", "moderate": "適度", "high": "高"}, "memoryArtStyle": "記憶アートスタイル", "memoryArtStyleDesc": "記憶アートワーク生成のデフォルトアートスタイル", "memoryArtStyleOptions": {"anime": "アニメスタイル", "realistic": "リアリスティックスタイル", "abstract": "抽象スタイル", "custom": "カスタムスタイル"}, "streakMotivation": "連続記録モチベーション", "streakMotivationDesc": "連続記録についての励ましメッセージを受信", "progressCelebrations": "進捗祝い", "progressCelebrationsDesc": "マイルストーン到達時に特別効果を表示", "competitiveMode": "競争モード", "competitiveModeDesc": "競争機能とリーダーボードを有効", "leaderboardVisibility": "リーダーボード表示", "leaderboardVisibilityDesc": "リーダーボードでの表示を制御", "leaderboardVisibilityOptions": {"public": "公開（すべての人）", "friends": "友達のみ", "private": "非公開（非表示）"}, "autoClaimRewards": "報酬自動受取", "autoClaimRewardsDesc": "利用可能な報酬を自動的に受け取る", "experienceDisplayMode": "経験表示", "experienceDisplayModeDesc": "経験と進捗の表示方法", "experienceDisplayModeOptions": {"detailed": "詳細（数値表示）", "simplified": "簡略（進捗バー）", "minimal": "ミニマル（マイルストーンのみ）"}, "badgeDisplayMode": "バッジ表示", "badgeDisplayModeDesc": "達成バッジの表示方法", "badgeDisplayModeOptions": {"all": "すべてのバッジ", "favorites": "お気に入りのみ", "recent": "最近のもののみ"}}, "dataManagement": {"title": "データ管理", "description": "データストレージとプライバシーを管理", "clearCache": "キャッシュクリア", "clearCacheDesc": "一時ファイルを削除してスペースを解放", "clearNow": "今すぐクリア", "exportPersonalData": "個人データエクスポート", "exportPersonalDataDesc": "チャット履歴と記憶カプセルをダウンロード", "requestExport": "エクスポート申請", "dataUsageStats": "データ使用統計", "dataUsageStatsDesc": "プラットフォーム使用データと統計を表示", "viewStats": "統計を表示", "autoMemoryBackup": "記憶自動バックアップ", "autoMemoryBackupDesc": "AI記憶をクラウドに自動バックアップ", "autoCleanup": "自動クリーンアップ", "autoCleanupDesc": "古い一時ファイルを自動削除", "dataRetentionPeriod": "データ保持", "dataRetentionPeriodDesc": "データを保持する期間（日数）", "cacheSize": "キャッシュサイズ", "cacheSizeDesc": "現在のキャッシュサイズ：{{size}}MB", "backupFrequency": "バックアップ頻度", "backupFrequencyDesc": "データをバックアップする頻度", "backupFrequencyOptions": {"daily": "毎日", "weekly": "毎週", "monthly": "毎月"}, "storageOptimization": "ストレージ最適化", "storageOptimizationDesc": "古いデータを圧縮してストレージを最適化", "compressionEnabled": "データ圧縮", "compressionEnabledDesc": "ストレージスペース節約のためデータを圧縮", "cloudSyncEnabled": "クラウド同期", "cloudSyncEnabledDesc": "デバイス間でデータを同期", "localStorageLimit": "ローカルストレージ制限", "localStorageLimitDesc": "最大ローカルストレージ（MB）", "downloadHistory": "ダウンロード履歴", "downloadHistoryDesc": "データダウンロードの記録を保持", "chatHistoryLimit": "チャット履歴制限", "chatHistoryLimitDesc": "保持する最大チャットメッセージ数"}, "premium": {"title": "プレミアム機能", "description": "ダイアモンドパス会員限定機能", "creatorTools": "クリエイターツール", "creatorToolsDesc": "高度なキャラクター作成と分析ツールにアクセス", "advancedAnalytics": "高度な分析", "advancedAnalyticsDesc": "キャラクターパフォーマンスの詳細な洞察", "prioritySupport": "優先サポート", "prioritySupportDesc": "サポートチームからより早い応答時間を獲得", "exclusiveContent": "限定コンテンツ", "exclusiveContentDesc": "プレミアムキャラクターとストーリーラインにアクセス", "whisperSpaceAccess": "ウィスパースペースアクセス", "whisperSpaceAccessDesc": "限定プレミアム会員ディスカッションに参加", "unlimitedFastRequests": "無制限高速リクエスト", "unlimitedFastRequestsDesc": "高速AI応答リクエストに制限なし", "enhancedMemoryCapacity": "拡張記憶容量", "enhancedMemoryCapacityDesc": "3倍大きい記憶カプセル容量", "whisperSpaceSettings": "ウィスパースペース設定", "whisperSpaceSettingsDesc": "プレミアムソーシャル特権を管理", "configureSettings": "設定を構成", "customUIThemes": "カスタムUIテーマ", "customUIThemesDesc": "限定インターフェーステーマにアクセス", "advancedFilters": "高度なフィルター", "advancedFiltersDesc": "より洗練されたコンテンツフィルタリングオプション", "betaFeatureAccess": "ベータ機能アクセス", "betaFeatureAccessDesc": "実験的機能への早期アクセス", "aiModelSelection": "AIモデル選択", "aiModelSelectionDesc": "複数のAIモデルから選択", "customPersonalities": "カスタム個性", "customPersonalitiesDesc": "カスタムAI個性を作成・使用"}}, "actions": {"save": "保存", "cancel": "キャンセル", "reset": "リセット", "export": "エクスポート", "import": "インポート", "delete": "削除", "change": "変更", "configure": "構成", "manage": "管理", "upgrade": "アップグレード", "enable": "有効", "disable": "無効"}, "messages": {"settingsSaved": "設定が正常に保存されました！", "settingsReset": "設定をデフォルトにリセットしました", "exportRequested": "データエクスポートを申請しました。準備ができましたらメールでお知らせします。", "cacheCleared": "キャッシュを正常にクリアしました", "confirmReset": "本当にすべての設定をデフォルトにリセットしますか？", "confirmDelete": "本当にアカウントを削除しますか？この操作は元に戻せません。", "confirmClearCache": "ストレージスペースを解放するためキャッシュをクリアしますか？", "upgradeRequired": "この機能にはダイアモンドパス会員資格が必要です", "featureComingSoon": "この機能は近日公開予定です！", "errorSaving": "設定保存エラーです。再度お試しください。", "errorLoading": "設定の読み込み中にエラーが発生しました。ページを更新してください。"}}, "auth": {"loginTitle": "Alphane.ai にサインイン", "registerTitle": "Alphane.ai アカウントを作成", "needAccount": "アカウントが必要ですか？", "alreadyHaveAccount": "すでにアカウントをお持ちですか？", "byContinuing": "続行することで、あなたは", "terms": "利用規約", "and": "および", "privacy": "プライバシーポリシー", "password": "パスワード", "smsEmailCode": "SMS/メールコード", "emailAddress": "メールアドレス", "verificationCode": "認証コード", "resend": "再送", "getCode": "コードを取得", "rememberMe": "ログイン状態を保持する", "forgotPassword": "パスワードをお忘れですか？", "signingIn": "サインイン中...", "continue": "続ける", "or": "または", "continueWithGoogle": "Google で続行", "dontHaveAccount": "アカウントをお持ちでないですか？", "signUp": "サインアップ", "verificationCodeOtp": "認証コード (OTP)", "confirmPassword": "パスワードを確認", "iAgree": "私は同意します", "creatingAccount": "アカウント作成中...", "emailPlaceholder": "<EMAIL>", "passwordPlaceholder": "パスワードを入力", "otpPlaceholder": "6桁コード", "passwordMinPlaceholder": "最低8文字", "passwordRepeatPlaceholder": "パスワードを再入力", "errors": {"emailRequired": "メールアドレスは必須です", "emailInvalid": "有効なメールアドレスを入力してください", "passwordRequired": "パスワードは必須です", "verificationCodeRequired": "認証コードは必須です", "verificationCodeLength": "認証コードは6桁である必要があります", "passwordMinLength": "パスワードは8文字以上である必要があります", "confirmPassword": "パスワードを確認してください", "passwordsNotMatch": "パスワードが一致しません", "agreeTerms": "利用規約とプライバシーポリシーに同意する必要があります"}}, "story": {"title": "ストーリー詳細", "header": {"back": "戻る", "continueStory": "ストーリーを続ける"}, "info": {"starring": "", "starring2": "主演", "estimatedDuration": "推定プレイ時間：", "creator": "クリエイター：", "plays": "回体験", "likes": "回いいね", "rating": "評価", "progress": "ストーリー進捗", "chapters": "チャプター", "completed": "完了", "current": "現在"}, "actions": {"like": "いいね", "share": "シェア", "favorite": "お気に入り"}, "sections": {"description": "ストーリー概要", "tags": "ストーリータグ", "openingMessage": "オープニングメッセージ", "chapterProgress": "チャプター進捗", "keyChoices": "重要な選択", "completionRewards": "完了報酬", "unlockableAchievements": "解放可能な実績", "participatingCharacters": "参加キャラクター", "unlockConditions": "解放条件"}, "chapters": {"title": "チャプター進捗", "enterChapter": "チャプターに入る：", "completed": "完了", "current": "現在", "locked": "ロック"}, "choices": {"title": "重要な選択", "description": "第{{chapter}}章で、重要な分岐選択に直面します：", "result": "結果："}, "rewards": {"moreRewards": "さらに{{count}}個の報酬..."}, "achievements": {"title": "解放可能な実績"}, "characters": {"title": "参加キャラクター", "mainRole": "主人公", "supportingRole": "サポートキャラクター", "minorRole": "マイナーキャラクター", "mainDescription": "、活発で明るい", "supportingDescription": "、ストーリーの重要人物", "bondLevel": "絆 Lv.{{level}}", "unlocked": "解放済み", "locked": "未解放"}, "unlock": {"title": "解放条件", "bondLevel": "{{character}}との絆レベルが{{level}}レベルに到達", "tutorialComplete": "チュートリアル完了", "monthlyPass": "月間パス特権を所有（推奨）"}, "currency": {"fire": "🔥", "diamond": "💎", "puzzle": "🧩", "drop": "💧"}}}